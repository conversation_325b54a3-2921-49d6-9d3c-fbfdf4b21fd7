const express = require('express');
const multer = require('multer');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ storage: storage });

// Real YOLOv8 analysis function using ultralytics
let yoloModel = null;

const initializeYOLO = async () => {
  try {
    // Dynamic import to avoid issues if ultralytics is not installed
    const { YOLO } = await import('ultralytics');
    yoloModel = new YOLO('best.pt');
    console.log('✅ YOLOv8 model loaded successfully: best.pt');
    return true;
  } catch (error) {
    console.log('⚠️ Ultralytics not available, using mock YOLOv8');
    console.log('To use real YOLOv8, install: npm install ultralytics');
    return false;
  }
};

const analyzeImageWithYOLO = async (imagePath) => {
  return new Promise((resolve, reject) => {
    const pythonProcess = spawn('python', ['yolo_detect.py', 'best.pt', imagePath], { cwd: __dirname });
    let output = '';
    let errorOutput = '';

    pythonProcess.stdout.on('data', (data) => {
      output += data.toString();
    });
    pythonProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    pythonProcess.on('close', (code) => {
      try {
        // Find the last JSON object in the output (in case there are progress messages)
        const lines = output.split('\n');
        let jsonOutput = '';
        for (let i = lines.length - 1; i >= 0; i--) {
          const line = lines[i].trim();
          if (line.startsWith('{') && line.endsWith('}')) {
            jsonOutput = line;
            break;
          }
        }
        
        if (!jsonOutput) {
          reject(new Error('No JSON output found from Python script'));
          return;
        }
        
        const result = JSON.parse(jsonOutput);
        if (result.error) {
          reject(new Error(result.error));
        } else {
          console.log('📊 Real YOLOv8 detections:', result.results);
          resolve({
            results: result.results
            // No longer returning annotated image path since bounding boxes are removed
          });
        }
      } catch (err) {
        console.error('❌ Python script output:', output);
        console.error('❌ Python script errors:', errorOutput);
        reject(new Error('Failed to parse YOLO output: ' + err.message));
      }
    });
  });
};

// API Routes
app.post('/api/analyze', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No image file provided' });
    }

    console.log('📸 Analyzing image:', req.file.filename);
    
    // Analyze image with YOLOv8
    const analysisResult = await analyzeImageWithYOLO(req.file.path);
    
    // Clean up uploaded file
    fs.unlinkSync(req.file.path);
    
    console.log('✅ Analysis results:', analysisResult.results);
    
    res.json({
      success: true,
      results: analysisResult.results,
      // No longer returning annotated image path since bounding boxes are removed
      model: 'best.pt',
      timestamp: new Date().toISOString(),
      imagePath: req.file.filename
    });
    
  } catch (error) {
    console.error('❌ Error analyzing image:', error);
    res.status(500).json({ 
      error: 'Failed to analyze image',
      details: error.message 
    });
  }
});

// Annotated image serving endpoint removed - no longer needed since bounding boxes are removed

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    model: 'best.pt',
    classes: ['decaycavity', 'early-decay', 'healthy tooth'],
    yoloLoaded: yoloModel !== null
  });
});

// Serve React app
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'build', 'index.html'));
});

// Initialize YOLOv8 on startup
initializeYOLO().then(() => {
  app.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📊 YOLOv8 model: best.pt`);
    console.log(`🔍 Classes: decaycavity, early-decay, healthy tooth`);
    console.log(`🌐 Open http://localhost:${PORT} to view the dashboard`);
  });
}); 