import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ide<PERSON>, <PERSON>a<PERSON>ser, FaChartBar, FaBrain, FaClock, FaEye, FaSearch, FaHistory, FaInfoCircle, FaTimesCircle, FaExclamationTriangle, FaCheckCircle, FaCalendarAlt, FaServer } from 'react-icons/fa';
import VideoCall from './components/VideoCall';
import YOLODetection from './components/YOLODetection';
import PatientInfo from './components/PatientInfo';
import AnalysisResults from './components/AnalysisResults';
import Sidebar from './components/Sidebar';
import Navbar from './components/Navbar';
import './App.css';

function App() {
  const [isConnected, setIsConnected] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [serverStatus, setServerStatus] = useState('checking');
  const [patientInfo] = useState({
    name: '<PERSON>',
    id: 'P001',
    age: 35,
    lastVisit: '2024-01-15'
  });
  const [detectionResults, setDetectionResults] = useState([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentCapturedImage, setCurrentCapturedImage] = useState(null);
  // Annotated image path removed - no longer using bounding box images
  const [analysisHistory, setAnalysisHistory] = useState([]);

  // Check server status
  const checkServerStatus = async () => {
    try {
      const response = await fetch('/api/health');
      if (response.ok) {
        const data = await response.json();
        setServerStatus('connected');
        console.log('✅ YOLOv8 server connected:', data);
      } else {
        setServerStatus('error');
        console.log('❌ YOLOv8 server health check failed');
      }
    } catch (error) {
      setServerStatus('disconnected');
      console.log('❌ YOLOv8 server not reachable:', error.message);
    }
  };

  useEffect(() => {
    checkServerStatus();
    // Check server status every 30 seconds
    const interval = setInterval(checkServerStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const handleConnectionStatus = (status) => {
    setIsConnected(status);
  };

  const handleDetectionResults = (results, annotatedImagePath) => {
    setDetectionResults(results);
    setIsAnalyzing(false);
    // No longer storing annotated image path since bounding boxes are removed

    // Add to history
    const newAnalysis = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      results: results,
      image: currentCapturedImage,
      // No longer storing annotated image since bounding boxes are removed
      patientId: patientInfo.id,
      patientName: patientInfo.name
    };
    
    setAnalysisHistory(prev => [newAnalysis, ...prev.slice(0, 19)]); // Keep last 20 analyses
    
    if (results.length > 0) {
      const classNames = results.map(result => result.class);
      console.info(`Detected: ${classNames.join(', ')}`);
    }
  };

  const startAnalysis = () => {
    if (serverStatus !== 'connected') {
      console.error('YOLOv8 server not connected. Please start the backend server first.');
      return;
    }
    
    setIsAnalyzing(true);
    console.info('Starting dental analysis...');
  };

  const handleImageCaptured = (imageSrc) => {
    setCurrentCapturedImage(imageSrc);
  };

  const getServerStatusColor = () => {
    switch (serverStatus) {
      case 'connected': return 'text-green-600';
      case 'disconnected': return 'text-red-600';
      case 'error': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const getServerStatusText = () => {
    switch (serverStatus) {
      case 'connected': return 'YOLOv8 Connected';
      case 'disconnected': return 'YOLOv8 Disconnected';
      case 'error': return 'YOLOv8 Error';
      default: return 'Checking...';
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} isConnected={isConnected} />

        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              {/* Header */}
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">
                    Intraoral Patient Dashboard
                  </h1>
                  <p className="text-[#333333]">Real-time dental analysis and consultation</p>
                  <div className="flex items-center mt-2">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getServerStatusColor()}`}>
                      <FaServer className="mr-1 h-3 w-3" />
                      {getServerStatusText()}
                    </span>
                  </div>
                </div>
                <div className="flex flex-col gap-3">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={startAnalysis}
                    disabled={isAnalyzing || serverStatus !== 'connected'}
                    className="w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FaBrain className="h-5 w-5 mr-2" />
                    {isAnalyzing ? 'Analyzing...' : 'Start Analysis'}
                  </motion.button>
                  {serverStatus !== 'connected' && (
                    <p className="text-xs text-red-600 text-center">
                      YOLOv8 server required for analysis
                    </p>
                  )}
                </div>
              </div>

              {/* Stats Cards */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8"
              >
                <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]">
                      <FaUser className="h-6 w-6" />
                    </div>
                    <div className="ml-4">
                      <h2 className="text-sm font-medium text-gray-500">Patient</h2>
                      <p className="text-2xl font-bold text-[#0077B6]">{patientInfo.name}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]">
                      <FaVideo className="h-6 w-6" />
                    </div>
                    <div className="ml-4">
                      <h2 className="text-sm font-medium text-gray-500">Video Status</h2>
                      <p className="text-2xl font-bold text-[#0077B6]">{isConnected ? 'Live' : 'Offline'}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]">
                      <FaEye className="h-6 w-6" />
                    </div>
                    <div className="ml-4">
                      <h2 className="text-sm font-medium text-gray-500">Detections</h2>
                      <p className="text-2xl font-bold text-[#0077B6]">{detectionResults.length}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]">
                      <FaHistory className="h-6 w-6" />
                    </div>
                    <div className="ml-4">
                      <h2 className="text-sm font-medium text-gray-500">Total Analyses</h2>
                      <p className="text-2xl font-bold text-[#0077B6]">{analysisHistory.length}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6">
                  <div className="flex items-center">
                    <div className={`p-3 rounded-full ${serverStatus === 'connected' ? 'bg-green-100 text-green-600' : serverStatus === 'error' ? 'bg-yellow-100 text-yellow-600' : 'bg-red-100 text-red-600'}`}>
                      <FaServer className="h-6 w-6" />
                    </div>
                    <div className="ml-4">
                      <h2 className="text-sm font-medium text-gray-500">YOLOv8 Server</h2>
                      <p className={`text-2xl font-bold ${getServerStatusColor()}`}>
                        {serverStatus === 'connected' ? 'Online' : serverStatus === 'error' ? 'Error' : 'Offline'}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Main Content - Split Layout */}
              <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8">
                {/* Live Video Section - Larger */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                  className="xl:col-span-2 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6"
                >
                  <div className="flex items-center mb-6">
                    <div className="p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3">
                      <FaVideo className="h-5 w-5" />
                    </div>
                    <h2 className="text-xl font-bold text-[#0077B6]">Live Video Consultation</h2>
                  </div>
                  <VideoCall
                    onConnectionStatus={handleConnectionStatus}
                    onStartAnalysis={startAnalysis}
                    onImageCaptured={handleImageCaptured}
                    onDetectionResults={handleDetectionResults}
                  />
                </motion.div>

                {/* Patient Info Section */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                  className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6"
                >
                  <div className="flex items-center mb-6">
                    <div className="p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3">
                      <FaUser className="h-5 w-5" />
                    </div>
                    <h2 className="text-xl font-bold text-[#0077B6]">Patient Information</h2>
                  </div>
                  <PatientInfo patient={patientInfo} />
                </motion.div>
              </div>

              {/* Analysis and Results Section */}
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                {/* AI Analysis Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6"
                >
                  <div className="flex items-center mb-6">
                    <div className="p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3">
                      <FaBrain className="h-5 w-5" />
                    </div>
                    <h2 className="text-xl font-bold text-[#0077B6]">AI Dental Analysis</h2>
                  </div>
                  <YOLODetection
                    onResults={handleDetectionResults}
                    isAnalyzing={isAnalyzing}
                    currentImage={currentCapturedImage}
                    // No longer passing annotated image path since bounding boxes are removed
                  />
                </motion.div>

                {/* Results Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6"
                >
                  <div className="flex items-center mb-6">
                    <div className="p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3">
                      <FaChartBar className="h-5 w-5" />
                    </div>
                    <h2 className="text-xl font-bold text-[#0077B6]">Analysis Results</h2>
                  </div>
                  <AnalysisResults
                    results={detectionResults}
                    isAnalyzing={isAnalyzing}
                    history={analysisHistory}
                  />
                </motion.div>
              </div>

              {/* Static Patient Summary Section - Always Visible */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="mt-8"
              >
                <div className="flex items-center mb-6">
                  <div className="p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3">
                    <FaHistory className="h-5 w-5" />
                  </div>
                  <h2 className="text-2xl font-bold text-[#0077B6]">Patient Summary & History</h2>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Summary Card */}
                  <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6">
                    <div className="flex items-center mb-4">
                      <div className="p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3">
                        <FaChartBar className="h-4 w-4" />
                      </div>
                      <h3 className="text-lg font-semibold text-[#0077B6]">Current Summary</h3>
                    </div>
                    
                    {detectionResults.length > 0 ? (
                      <div className="space-y-4">
                        <div className="bg-[rgba(0,119,182,0.05)] p-4 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-700">Total Detections</span>
                            <span className="text-lg font-bold text-[#0077B6]">{detectionResults.length}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-700">Primary Issue</span>
                            <span className="text-sm font-semibold text-gray-900 capitalize">
                              {detectionResults[0]?.class.replace('-', ' ') || 'None'}
                            </span>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          {detectionResults.map((result, index) => (
                            <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                              <span className="text-sm font-medium text-gray-700 capitalize">
                                {result.class.replace('-', ' ')}
                              </span>
                              <span className="text-sm font-bold text-[#0077B6]">
                                {(result.confidence * 100).toFixed(1)}%
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <FaSearch className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                        <p className="text-gray-600 font-medium mb-2">No current analysis</p>
                        <p className="text-gray-500 text-sm">Start analysis to see summary</p>
                      </div>
                    )}
                  </div>

                  {/* Recommendations Card */}
                  <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6">
                    <div className="flex items-center mb-4">
                      <div className="p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3">
                        <FaInfoCircle className="h-4 w-4" />
                      </div>
                      <h3 className="text-lg font-semibold text-[#0077B6]">Recommendations</h3>
                    </div>
                    
                    {detectionResults.length > 0 ? (
                      <div className="space-y-3">
                        {(() => {
                          const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;
                          const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;
                          const healthyCount = detectionResults.filter(r => r.class === 'healthy tooth').length;
                          const recommendations = [];

                          if (decayCount > 0) {
                            recommendations.push({
                              type: 'urgent',
                              title: 'Immediate Treatment',
                              description: 'Cavities require immediate dental treatment.',
                              icon: <FaTimesCircle className="h-4 w-4 text-red-500" />
                            });
                          }

                          if (earlyDecayCount > 0) {
                            recommendations.push({
                              type: 'warning',
                              title: 'Preventive Care',
                              description: 'Schedule follow-up for preventive treatment.',
                              icon: <FaExclamationTriangle className="h-4 w-4 text-yellow-500" />
                            });
                          }

                          if (healthyCount > 0) {
                            recommendations.push({
                              type: 'positive',
                              title: 'Good Oral Health',
                              description: 'Continue regular oral hygiene routine.',
                              icon: <FaCheckCircle className="h-4 w-4 text-green-500" />
                            });
                          }

                          recommendations.push({
                            type: 'info',
                            title: 'Regular Checkup',
                            description: 'Schedule next checkup within 6 months.',
                            icon: <FaCalendarAlt className="h-4 w-4 text-blue-500" />
                          });

                          return recommendations.map((rec, index) => (
                            <div key={index} className="flex items-start p-3 bg-gray-50 rounded-lg">
                              <div className="mr-3 mt-1">
                                {rec.icon}
                              </div>
                              <div>
                                <h4 className="text-sm font-semibold text-gray-900">{rec.title}</h4>
                                <p className="text-xs text-gray-600">{rec.description}</p>
                              </div>
                            </div>
                          ));
                        })()}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <FaInfoCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                        <p className="text-gray-600 font-medium mb-2">No recommendations</p>
                        <p className="text-gray-500 text-sm">Complete analysis for recommendations</p>
                      </div>
                    )}
                  </div>

                  {/* Analysis History Card */}
                  <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6">
                    <div className="flex items-center mb-4">
                      <div className="p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3">
                        <FaHistory className="h-4 w-4" />
                      </div>
                      <h3 className="text-lg font-semibold text-[#0077B6]">Analysis History</h3>
                    </div>
                    
                    {analysisHistory.length > 0 ? (
                      <div className="space-y-3 max-h-64 overflow-y-auto">
                        {analysisHistory.slice(0, 5).map((historyItem, index) => {
                          const decayCount = historyItem.results.filter(r => r.class === 'decaycavity').length;
                          const earlyDecayCount = historyItem.results.filter(r => r.class === 'early-decay').length;
                          const healthyCount = historyItem.results.filter(r => r.class === 'healthy tooth').length;
                          
                          let severity = 'low';
                          let icon = <FaCheckCircle className="h-4 w-4 text-green-500" />;
                          
                          if (decayCount > 0) {
                            severity = 'high';
                            icon = <FaTimesCircle className="h-4 w-4 text-red-500" />;
                          } else if (earlyDecayCount > 0) {
                            severity = 'medium';
                            icon = <FaExclamationTriangle className="h-4 w-4 text-yellow-500" />;
                          }

                          return (
                            <div key={historyItem.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center">
                                <div className="mr-3">
                                  {icon}
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-gray-900">
                                    {historyItem.results.length} detection(s)
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    {new Date(historyItem.timestamp).toLocaleDateString()}
                                  </p>
                                </div>
                              </div>
                              <span className="px-2 py-1 text-xs font-medium bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full">
                                {severity}
                              </span>
                            </div>
                          );
                        })}
                        
                        {analysisHistory.length > 5 && (
                          <div className="text-center pt-2">
                            <p className="text-xs text-gray-500">
                              +{analysisHistory.length - 5} more analyses
                            </p>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <FaHistory className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                        <p className="text-gray-600 font-medium mb-2">No analysis history</p>
                        <p className="text-gray-500 text-sm">Perform analyses to build history</p>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default App; 