import sys
import json
import os
from ultralytics import YOLO
import cv2
import numpy as np

# Suppress YOLOv8 progress output
os.environ['ULTRALYTICS_VERBOSE'] = 'False'

if len(sys.argv) < 3:
    print(json.dumps({"error": "Usage: python yolo_detect.py <model_path> <image_path>"}))
    sys.exit(1)

model_path = sys.argv[1]
image_path = sys.argv[2]

try:
    # Suppress stdout temporarily to capture YOLOv8 output
    import io
    import contextlib
    
    # Capture YOLOv8 output
    f = io.StringIO()
    with contextlib.redirect_stdout(f):
        model = YOLO(model_path)
        results = model(image_path, verbose=False)  # Disable verbose output
    
    detections = []
    names = model.names
    
    # Load the original image (no annotation needed)
    original_image = cv2.imread(image_path)
    # No longer creating annotated image with bounding boxes
    
    for r in results:
        boxes = r.boxes
        if boxes is not None and hasattr(boxes, 'xyxy'):
            for i in range(len(boxes)):
                box = boxes.xyxy[i].tolist()
                conf = boxes.conf[i].item()
                cls = int(boxes.cls[i].item())
                class_name = names[cls] if cls in names else str(cls)
                x1, y1, x2, y2 = box
                img_w, img_h = r.orig_shape[1], r.orig_shape[0]
                
                # Bounding box drawing removed - no longer annotating images with boxes
                
                detections.append({
                    "class": class_name,
                    "confidence": conf,
                    "bbox": {
                        "x": x1 / img_w,
                        "y": y1 / img_h,
                        "width": (x2 - x1) / img_w,
                        "height": (y2 - y1) / img_h
                    }
                })
    
    # No longer saving annotated image with bounding boxes
    # Just use the original image path for compatibility
    annotated_path = image_path
    
    # Print only the JSON result
    print(json.dumps({
        "success": True, 
        "results": detections,
        "annotated_image_path": annotated_path
    }))
    
except Exception as e:
    print(json.dumps({"error": str(e)}))
    sys.exit(1) 