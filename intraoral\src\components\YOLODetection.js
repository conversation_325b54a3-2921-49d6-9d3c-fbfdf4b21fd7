import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { FaBrain, FaChartBar, FaClock, FaImage, FaRobot, FaExclamationTriangle, FaCheckCircle, FaTimesCircle, FaCalendarAlt, FaTooth, FaEye, FaServer } from 'react-icons/fa';
import './YOLODetection.css';

const YOLODetection = ({ onResults, isAnalyzing, currentImage, annotatedImagePath }) => {
  const [detectionHistory, setDetectionHistory] = useState([]);
  const [annotatedImage, setAnnotatedImage] = useState(null);
  const [detectionStats, setDetectionStats] = useState({
    decaycavity: 0,
    'early-decay': 0,
    'healthy tooth': 0
  });
  const [serverStatus, setServerStatus] = useState('checking');

  const classColors = {
    'decaycavity': '#ff6b6b',
    'early-decay': '#ffd43b',
    'healthy tooth': '#51cf66'
  };

  const classIcons = {
    'decaycavity': <FaTimesCircle className="h-6 w-6 text-red-500" />,
    'early-decay': <FaExclamationTriangle className="h-6 w-6 text-yellow-500" />,
    'healthy tooth': <FaCheckCircle className="h-6 w-6 text-green-500" />
  };

  // Check server status
  const checkServerStatus = useCallback(async () => {
    try {
      const response = await fetch('/api/health');
      if (response.ok) {
        const data = await response.json();
        setServerStatus('connected');
        console.log('✅ Server connected:', data);
      } else {
        setServerStatus('error');
        console.log('❌ Server health check failed');
      }
    } catch (error) {
      setServerStatus('disconnected');
      console.log('❌ Server not reachable:', error.message);
    }
  }, []);

  useEffect(() => {
    checkServerStatus();
    // Check server status every 30 seconds
    const interval = setInterval(checkServerStatus, 30000);
    return () => clearInterval(interval);
  }, [checkServerStatus]);

  useEffect(() => {
    // Update current image when prop changes
    if (currentImage) {
      console.log('📸 Received new captured image for analysis');
    }
  }, [currentImage]);

  useEffect(() => {
    // No longer processing annotated images with bounding boxes
    // This effect is kept for compatibility but does nothing
  }, [annotatedImagePath]);

  const drawDetectionBoxes = useCallback((ctx, results, canvasWidth, canvasHeight) => {
    // Bounding boxes removed - no longer drawing detection boxes on images
    // This function is kept for compatibility but does not draw anything
  }, []);

  const generateAnnotatedImage = useCallback((results) => {
    // No longer generating annotated images with bounding boxes
    // This function is kept for compatibility but does nothing
    setAnnotatedImage(null);
  }, []);

  const handleDetectionResults = useCallback((results) => {
    const newDetection = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      results: results,
      image: currentImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5Ij5JbWFnZSBQbGFjZWhvbGRlcjwvdGV4dD4KPC9zdmc+',
      // No longer storing annotated images with bounding boxes
      annotatedImage: null,
      annotatedImageUrl: null
    };

    setDetectionHistory(prev => [newDetection, ...prev.slice(0, 9)]);

    // Update stats
    const newStats = { ...detectionStats };
    results.forEach(result => {
      newStats[result.class]++;
    });
    setDetectionStats(newStats);

    // Notify parent component
    onResults(results);
  }, [currentImage, detectionStats, onResults]);

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return '#51cf66';
    if (confidence >= 0.6) return '#ffd43b';
    return '#ff6b6b';
  };

  const getServerStatusColor = () => {
    switch (serverStatus) {
      case 'connected': return 'text-green-600';
      case 'disconnected': return 'text-red-600';
      case 'error': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const getServerStatusText = () => {
    switch (serverStatus) {
      case 'connected': return 'Connected';
      case 'disconnected': return 'Disconnected';
      case 'error': return 'Error';
      default: return 'Checking...';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Model Info */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center">
          <div className="p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3">
            <FaBrain className="h-4 w-4" />
          </div>
          <h3 className="text-lg font-semibold text-[#0077B6]">AI Analysis</h3>
        </div>
        <div className="flex gap-2">
          <span className="px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium">
            <FaRobot className="inline mr-1" />
            Model: best.pt
          </span>
          <span className="px-3 py-1 bg-[rgba(32,178,170,0.1)] text-[#20B2AA] rounded-full text-xs font-medium">
            Classes: 3
          </span>
          <span className={`px-3 py-1 bg-[rgba(0,119,182,0.1)] rounded-full text-xs font-medium flex items-center ${getServerStatusColor()}`}>
            <FaServer className="inline mr-1" />
            {getServerStatusText()}
          </span>
        </div>
      </div>

      {/* Server Status Warning */}
      {serverStatus === 'disconnected' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-50 border border-red-200 p-4 rounded-lg"
        >
          <div className="flex items-center">
            <FaExclamationTriangle className="h-5 w-5 text-red-500 mr-3" />
            <div>
              <h4 className="text-sm font-medium text-red-800">YOLOv8 Server Not Connected</h4>
              <p className="text-sm text-red-700 mt-1">
                Please start the backend server to enable AI analysis. Run: <code className="bg-red-100 px-2 py-1 rounded">npm run server</code>
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Analysis Status */}
      {isAnalyzing && serverStatus === 'connected' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[#20B2AA] border-opacity-30"
        >
          <div className="flex items-center justify-center">
            <div className="loading-spinner mr-3"></div>
            <p className="text-[#0077B6] font-medium">Analyzing image with YOLOv8...</p>
          </div>
        </motion.div>
      )}

      {/* No Analysis Available */}
      {!isAnalyzing && serverStatus === 'disconnected' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-50 p-6 rounded-lg border border-gray-200"
        >
          <div className="flex items-center justify-center">
            <FaServer className="h-8 w-8 text-gray-400 mr-3" />
            <p className="text-gray-600 font-medium">AI analysis unavailable - server not connected</p>
          </div>
        </motion.div>
      )}

      {/* Annotated Image section removed - no longer displaying bounding boxes */}

      {/* Detection Statistics */}
      <div className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg">
        <div className="flex items-center mb-4">
          <div className="p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3">
            <FaChartBar className="h-4 w-4" />
          </div>
          <h4 className="text-lg font-semibold text-[#0077B6]">Detection Statistics</h4>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          {Object.entries(detectionStats).map(([className, count]) => (
            <motion.div
              key={className}
              whileHover={{ scale: 1.05 }}
              className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300"
            >
              <div className="flex items-center">
                <div className="mr-3">{classIcons[className]}</div>
                <div>
                  <p className="text-sm font-medium text-gray-700 capitalize">{className.replace('-', ' ')}</p>
                  <p className="text-2xl font-bold text-[#0077B6]">{count}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Detection History */}
      <div className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg">
        <div className="flex items-center mb-4">
          <div className="p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3">
            <FaClock className="h-4 w-4" />
          </div>
          <h4 className="text-lg font-semibold text-[#0077B6]">Recent Detections</h4>
        </div>

        <div className="space-y-3 max-h-64 overflow-y-auto">
          {detectionHistory.length === 0 ? (
            <div className="text-center py-8">
              <FaEye className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600 font-medium mb-2">No detections yet</p>
              <p className="text-gray-500 text-sm">Start video stream to begin analysis</p>
            </div>
          ) : (
            detectionHistory.map((detection) => (
              <motion.div
                key={detection.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300"
              >
                <div className="flex items-start space-x-3">
                  {/* Annotated Image Thumbnail removed - no longer showing bounding box images */}

                  {/* Detection Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <div className="p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3">
                          <FaTooth className="h-4 w-4" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {detection.results.length} detection(s)
                          </p>
                          <p className="text-xs text-gray-500">
                            {new Date(detection.timestamp).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    {/* Detection Results */}
                    <div className="flex items-center space-x-2 flex-wrap">
                      {detection.results.slice(0, 3).map((result, index) => (
                        <div key={index} className="flex items-center bg-gray-50 px-2 py-1 rounded">
                          {classIcons[result.class]}
                          <span className="ml-1 text-xs font-medium text-gray-600">
                            {(result.confidence * 100).toFixed(0)}%
                          </span>
                        </div>
                      ))}
                      {detection.results.length > 3 && (
                        <span className="text-xs text-gray-500">
                          +{detection.results.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default YOLODetection; 