[{"D:\\intraoral\\src\\index.js": "1", "D:\\intraoral\\src\\App.js": "2", "D:\\intraoral\\src\\components\\YOLODetection.js": "3", "D:\\intraoral\\src\\components\\PatientInfo.js": "4", "D:\\intraoral\\src\\components\\AnalysisResults.js": "5", "D:\\intraoral\\src\\components\\VideoCall.js": "6", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\index.js": "7", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\App.js": "8", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\VideoCall.js": "9", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\YOLODetection.js": "10", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\PatientInfo.js": "11", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\AnalysisResults.js": "12", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\Sidebar.jsx": "13", "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\Navbar.jsx": "14"}, {"size": 264, "mtime": 1750455288629, "results": "15", "hashOfConfig": "16"}, {"size": 3419, "mtime": 1750456579279, "results": "17", "hashOfConfig": "16"}, {"size": 7859, "mtime": 1750456616189, "results": "18", "hashOfConfig": "16"}, {"size": 5261, "mtime": 1750459054072, "results": "19", "hashOfConfig": "16"}, {"size": 8018, "mtime": 1750459054072, "results": "20", "hashOfConfig": "16"}, {"size": 6883, "mtime": 1750456564794, "results": "21", "hashOfConfig": "16"}, {"size": 264, "mtime": 1750455288629, "results": "22", "hashOfConfig": "23"}, {"size": 25840, "mtime": 1750706079497, "results": "24", "hashOfConfig": "23"}, {"size": 13978, "mtime": 1750706024300, "results": "25", "hashOfConfig": "23"}, {"size": 12147, "mtime": 1750705905658, "results": "26", "hashOfConfig": "23"}, {"size": 9602, "mtime": 1750531792859, "results": "27", "hashOfConfig": "23"}, {"size": 17392, "mtime": 1750544379484, "results": "28", "hashOfConfig": "23"}, {"size": 3379, "mtime": 1750542923303, "results": "29", "hashOfConfig": "23"}, {"size": 4035, "mtime": 1750542939690, "results": "30", "hashOfConfig": "23"}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wlzyog", {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "troter", {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\intraoral\\src\\index.js", [], [], "D:\\intraoral\\src\\App.js", ["73", "74"], [], "D:\\intraoral\\src\\components\\YOLODetection.js", ["75", "76"], [], "D:\\intraoral\\src\\components\\PatientInfo.js", [], [], "D:\\intraoral\\src\\components\\AnalysisResults.js", [], [], "D:\\intraoral\\src\\components\\VideoCall.js", ["77", "78", "79"], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\index.js", [], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\App.js", ["80", "81", "82"], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\VideoCall.js", [], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\YOLODetection.js", ["83", "84", "85", "86", "87", "88", "89", "90"], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\PatientInfo.js", [], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\AnalysisResults.js", ["91", "92", "93"], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\Sidebar.jsx", [], [], "D:\\Dentlyzer_Final - Copy\\intraoral\\src\\components\\Navbar.jsx", [], [], {"ruleId": "94", "severity": 1, "message": "95", "line": 1, "column": 27, "nodeType": "96", "messageId": "97", "endLine": 1, "endColumn": 36}, {"ruleId": "94", "severity": 1, "message": "98", "line": 12, "column": 23, "nodeType": "96", "messageId": "97", "endLine": 12, "endColumn": 37}, {"ruleId": "94", "severity": 1, "message": "99", "line": 2, "column": 10, "nodeType": "96", "messageId": "97", "endLine": 2, "endColumn": 15}, {"ruleId": "100", "severity": 1, "message": "101", "line": 41, "column": 6, "nodeType": "102", "endLine": 41, "endColumn": 19, "suggestions": "103"}, {"ruleId": "100", "severity": 1, "message": "104", "line": 30, "column": 6, "nodeType": "102", "endLine": 30, "endColumn": 39, "suggestions": "105"}, {"ruleId": "100", "severity": 1, "message": "106", "line": 86, "column": 6, "nodeType": "102", "endLine": 86, "endColumn": 36, "suggestions": "107"}, {"ruleId": "100", "severity": 1, "message": "108", "line": 140, "column": 6, "nodeType": "102", "endLine": 140, "endColumn": 49, "suggestions": "109"}, {"ruleId": "94", "severity": 1, "message": "110", "line": 3, "column": 10, "nodeType": "96", "messageId": "97", "endLine": 3, "endColumn": 17}, {"ruleId": "94", "severity": 1, "message": "111", "line": 3, "column": 57, "nodeType": "96", "messageId": "97", "endLine": 3, "endColumn": 64}, {"ruleId": "94", "severity": 1, "message": "112", "line": 462, "column": 33, "nodeType": "96", "messageId": "97", "endLine": 462, "endColumn": 45}, {"ruleId": "94", "severity": 1, "message": "113", "line": 3, "column": 40, "nodeType": "96", "messageId": "97", "endLine": 3, "endColumn": 47}, {"ruleId": "94", "severity": 1, "message": "114", "line": 3, "column": 111, "nodeType": "96", "messageId": "97", "endLine": 3, "endColumn": 124}, {"ruleId": "94", "severity": 1, "message": "115", "line": 8, "column": 10, "nodeType": "96", "messageId": "97", "endLine": 8, "endColumn": 24}, {"ruleId": "94", "severity": 1, "message": "116", "line": 16, "column": 9, "nodeType": "96", "messageId": "97", "endLine": 16, "endColumn": 20}, {"ruleId": "94", "severity": 1, "message": "117", "line": 65, "column": 9, "nodeType": "96", "messageId": "97", "endLine": 65, "endColumn": 27}, {"ruleId": "94", "severity": 1, "message": "118", "line": 70, "column": 9, "nodeType": "96", "messageId": "97", "endLine": 70, "endColumn": 31}, {"ruleId": "94", "severity": 1, "message": "119", "line": 76, "column": 9, "nodeType": "96", "messageId": "97", "endLine": 76, "endColumn": 31}, {"ruleId": "94", "severity": 1, "message": "120", "line": 100, "column": 9, "nodeType": "96", "messageId": "97", "endLine": 100, "endColumn": 27}, {"ruleId": "94", "severity": 1, "message": "121", "line": 3, "column": 23, "nodeType": "96", "messageId": "97", "endLine": 3, "endColumn": 32}, {"ruleId": "94", "severity": 1, "message": "122", "line": 3, "column": 34, "nodeType": "96", "messageId": "97", "endLine": 3, "endColumn": 41}, {"ruleId": "94", "severity": 1, "message": "111", "line": 3, "column": 135, "nodeType": "96", "messageId": "97", "endLine": 3, "endColumn": 142}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'setPatientInfo' is assigned a value but never used.", "'toast' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleDetectionResults'. Either include it or remove the dependency array.", "ArrayExpression", ["123"], "React Hook useCallback has a missing dependency: 'startAutoCapture'. Either include it or remove the dependency array.", ["124"], "React Hook useCallback has a missing dependency: 'sendImageForAnalysis'. Either include it or remove the dependency array.", ["125"], "React Hook useEffect has a missing dependency: 'startAutoCapture'. Either include it or remove the dependency array.", ["126"], "'FaTooth' is defined but never used.", "'FaClock' is defined but never used.", "'healthyCount' is assigned a value but never used.", "'FaImage' is defined but never used.", "'FaCalendarAlt' is defined but never used.", "'annotatedImage' is assigned a value but never used.", "'classColors' is assigned a value but never used.", "'drawDetectionBoxes' is assigned a value but never used.", "'generateAnnotatedImage' is assigned a value but never used.", "'handleDetectionResults' is assigned a value but never used.", "'getConfidenceColor' is assigned a value but never used.", "'FaFileAlt' is defined but never used.", "'FaPhone' is defined but never used.", {"desc": "127", "fix": "128"}, {"desc": "129", "fix": "130"}, {"desc": "131", "fix": "132"}, {"desc": "133", "fix": "134"}, "Update the dependencies array to be: [handleDetectionResults, isAnalyzing]", {"range": "135", "text": "136"}, "Update the dependencies array to be: [autoCapture, onConnectionStatus, startAutoCapture]", {"range": "137", "text": "138"}, "Update the dependencies array to be: [isStreaming, onImageCaptured, sendImageForAnalysis]", {"range": "139", "text": "140"}, "Update the dependencies array to be: [autoCapture, captureInterval, isStreaming, startAutoCapture]", {"range": "141", "text": "142"}, [1131, 1144], "[handleDetectionResults, isAnalyzing]", [1003, 1036], "[autoCapture, onConnectionStatus, startAutoCapture]", [2551, 2581], "[isStreaming, onImageCaptured, sendImageForAnalysis]", [3945, 3988], "[autoCapture, captureInterval, isStreaming, startAutoCapture]"]