{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\VideoCall.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport Webcam from 'react-webcam';\nimport { motion } from 'framer-motion';\nimport { FaPlay, FaStop, FaCamera, FaImage, FaVideo, FaCog } from 'react-icons/fa';\nimport './VideoCall.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoCall = ({\n  onConnectionStatus,\n  onStartAnalysis,\n  onImageCaptured,\n  onDetectionResults\n}) => {\n  _s();\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [isCapturing, setIsCapturing] = useState(false);\n  const [capturedImages, setCapturedImages] = useState([]);\n  const [autoCapture, setAutoCapture] = useState(true);\n  const [captureInterval, setCaptureInterval] = useState(5000); // 5 seconds\n  const [availableCameras, setAvailableCameras] = useState([]);\n  const [selectedCamera, setSelectedCamera] = useState(null);\n  const [showCameraSettings, setShowCameraSettings] = useState(false);\n  const [mirrorCamera, setMirrorCamera] = useState(true); // Enable mirroring by default for USB cameras\n  const webcamRef = useRef(null);\n  const intervalRef = useRef(null);\n\n  // Get available cameras\n  const getAvailableCameras = useCallback(async () => {\n    try {\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      const videoDevices = devices.filter(device => device.kind === 'videoinput');\n      setAvailableCameras(videoDevices);\n\n      // Auto-select first USB camera or fallback to first available\n      const usbCamera = videoDevices.find(device => device.label.toLowerCase().includes('usb') || device.label.toLowerCase().includes('external') || device.label.toLowerCase().includes('camera'));\n      if (usbCamera) {\n        setSelectedCamera(usbCamera.deviceId);\n      } else if (videoDevices.length > 0) {\n        setSelectedCamera(videoDevices[0].deviceId);\n      }\n    } catch (error) {\n      console.error('Error getting cameras:', error);\n    }\n  }, []);\n  const videoConstraints = {\n    width: 640,\n    height: 480,\n    deviceId: selectedCamera ? {\n      exact: selectedCamera\n    } : undefined\n  };\n  const sendImageForAnalysis = useCallback(async imageSrc => {\n    try {\n      onStartAnalysis();\n\n      // Convert base64 to blob\n      const base64Data = imageSrc.replace(/^data:image\\/jpeg;base64,/, '');\n      const blob = await fetch(`data:image/jpeg;base64,${base64Data}`).then(res => res.blob());\n\n      // Create FormData\n      const formData = new FormData();\n      formData.append('image', blob, 'capture.jpg');\n\n      // Send to backend for YOLOv8 analysis\n      const response = await fetch('/api/analyze', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        throw new Error('Analysis failed');\n      }\n      const results = await response.json();\n      if (onDetectionResults && results && results.results) {\n        onDetectionResults(results.results, results.annotatedImagePath);\n      }\n    } catch (error) {\n      console.error('Error sending image for analysis:', error);\n      if (onDetectionResults) {\n        onDetectionResults([], null);\n      }\n    }\n  }, [onStartAnalysis, onDetectionResults]);\n  const captureImage = useCallback(() => {\n    if (webcamRef.current && isStreaming) {\n      setIsCapturing(true);\n      const imageSrc = webcamRef.current.getScreenshot();\n      if (imageSrc) {\n        const newImage = {\n          id: Date.now(),\n          src: imageSrc,\n          timestamp: new Date().toISOString(),\n          analyzed: false\n        };\n        setCapturedImages(prev => [newImage, ...prev.slice(0, 9)]); // Keep last 10 images\n\n        // Pass the captured image to parent component for YOLOv8 analysis\n        if (onImageCaptured) {\n          onImageCaptured(imageSrc);\n        }\n\n        // Send to YOLOv8 analysis\n        sendImageForAnalysis(imageSrc);\n      }\n      setIsCapturing(false);\n    }\n  }, [isStreaming, onImageCaptured, sendImageForAnalysis]);\n  const startAutoCapture = useCallback(() => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n    }\n    intervalRef.current = setInterval(() => {\n      if (isStreaming && webcamRef.current) {\n        captureImage();\n      }\n    }, captureInterval);\n  }, [isStreaming, captureInterval, captureImage]);\n  const startStream = useCallback(() => {\n    if (!selectedCamera) {\n      return;\n    }\n    setIsStreaming(true);\n    onConnectionStatus(true);\n\n    // Start automatic capture if enabled\n    if (autoCapture) {\n      startAutoCapture();\n    }\n  }, [autoCapture, onConnectionStatus, startAutoCapture, selectedCamera]);\n  const stopStream = useCallback(() => {\n    setIsStreaming(false);\n    onConnectionStatus(false);\n    stopAutoCapture();\n  }, [onConnectionStatus]);\n  const stopAutoCapture = () => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  };\n  const toggleAutoCapture = () => {\n    setAutoCapture(!autoCapture);\n    if (!autoCapture) {\n      startAutoCapture();\n    } else {\n      stopAutoCapture();\n    }\n  };\n  const handleCameraChange = deviceId => {\n    setSelectedCamera(deviceId);\n    if (isStreaming) {\n      stopStream();\n      setTimeout(() => {\n        startStream();\n      }, 500);\n    }\n  };\n  useEffect(() => {\n    getAvailableCameras();\n  }, [getAvailableCameras]);\n  useEffect(() => {\n    return () => {\n      stopAutoCapture();\n    };\n  }, []);\n  useEffect(() => {\n    if (autoCapture && isStreaming) {\n      startAutoCapture();\n    } else {\n      stopAutoCapture();\n    }\n  }, [autoCapture, captureInterval, isStreaming, startAutoCapture]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-50 p-4 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-gray-700\",\n          children: \"Camera Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCameraSettings(!showCameraSettings),\n          className: \"flex items-center text-sm text-[#0077B6] hover:text-[#20B2AA]\",\n          children: [/*#__PURE__*/_jsxDEV(FaCog, {\n            className: \"mr-1 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), showCameraSettings ? 'Hide' : 'Show', \" Settings\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), showCameraSettings && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-medium text-gray-700\",\n          children: \"Select Camera:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedCamera || '',\n          onChange: e => handleCameraChange(e.target.value),\n          className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select a camera...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this), availableCameras.map(camera => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: camera.deviceId,\n            children: camera.label || `Camera ${camera.deviceId.slice(0, 8)}...`\n          }, camera.deviceId, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            id: \"mirrorCamera\",\n            checked: mirrorCamera,\n            onChange: e => setMirrorCamera(e.target.checked),\n            className: \"mr-2 h-4 w-4 text-[#0077B6] focus:ring-[#20B2AA] border-gray-300 rounded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"mirrorCamera\",\n            className: \"text-sm font-medium text-gray-700\",\n            children: \"Mirror Camera (Fix reversed image)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500\",\n          children: [availableCameras.length, \" camera(s) detected\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap gap-3 items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: `flex items-center px-4 py-2 rounded-full font-medium transition-all duration-300 ${isStreaming ? 'bg-gradient-to-r from-[#ef4444] to-[#dc2626] text-white shadow-md hover:shadow-lg' : 'bg-gradient-to-r from-[#22c55e] to-[#16a34a] text-white shadow-md hover:shadow-lg'}`,\n          onClick: isStreaming ? stopStream : startStream,\n          disabled: !selectedCamera,\n          children: [isStreaming ? /*#__PURE__*/_jsxDEV(FaStop, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 28\n          }, this) : /*#__PURE__*/_jsxDEV(FaPlay, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 66\n          }, this), isStreaming ? 'Stop Stream' : 'Start Stream']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: \"flex items-center px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n          onClick: captureImage,\n          disabled: !isStreaming || isCapturing,\n          children: [/*#__PURE__*/_jsxDEV(FaCamera, {\n            className: \"mr-2 h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), isCapturing ? 'Capturing...' : 'Capture Image']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-center text-sm font-medium text-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: autoCapture,\n            onChange: toggleAutoCapture,\n            disabled: !isStreaming,\n            className: \"mr-2 h-4 w-4 text-[#0077B6] focus:ring-[#20B2AA] border-gray-300 rounded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), \"Auto Capture\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), autoCapture && /*#__PURE__*/_jsxDEV(\"select\", {\n          value: captureInterval,\n          onChange: e => setCaptureInterval(Number(e.target.value)),\n          disabled: !isStreaming,\n          className: \"px-3 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: 3000,\n            children: \"Every 3s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 5000,\n            children: \"Every 5s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: 10000,\n            children: \"Every 10s\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"flex items-center text-sm font-medium text-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: mirrorCamera,\n            onChange: e => setMirrorCamera(e.target.checked),\n            className: \"mr-2 h-4 w-4 text-[#0077B6] focus:ring-[#20B2AA] border-gray-300 rounded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), \"Mirror\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative bg-gray-900 rounded-xl overflow-hidden shadow-lg\",\n      children: [isStreaming ? /*#__PURE__*/_jsxDEV(Webcam, {\n        ref: webcamRef,\n        audio: false,\n        screenshotFormat: \"image/jpeg\",\n        videoConstraints: videoConstraints,\n        mirrored: mirrorCamera,\n        className: \"w-full h-80 md:h-96 lg:h-[500px] object-cover\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full h-80 md:h-96 lg:h-[500px] flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center text-white\",\n          children: [/*#__PURE__*/_jsxDEV(FaVideo, {\n            className: \"h-16 w-16 mx-auto mb-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg font-medium\",\n            children: \"Click \\\"Start Stream\\\" to begin video consultation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-400 mt-2\",\n            children: \"Live dental examination and analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this), isCapturing && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white px-6 py-3 rounded-full flex items-center text-[#0077B6] font-medium\",\n          children: [/*#__PURE__*/_jsxDEV(FaCamera, {\n            className: \"mr-2 h-5 w-5 animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this), \"Capturing...\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaImage, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: [\"Recent Captures (\", capturedImages.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), capturedImages.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n        children: capturedImages.map(image => /*#__PURE__*/_jsxDEV(motion.div, {\n          whileHover: {\n            scale: 1.05\n          },\n          className: \"relative bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: image.src,\n            alt: \"Captured\",\n            className: \"w-full h-24 object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-600 mb-1\",\n              children: new Date(image.timestamp).toLocaleTimeString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${image.analyzed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`,\n              children: image.analyzed ? '✓ Analyzed' : '⏳ Pending'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 17\n          }, this)]\n        }, image.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-8 text-gray-500\",\n        children: [/*#__PURE__*/_jsxDEV(FaImage, {\n          className: \"h-12 w-12 mx-auto mb-3 opacity-50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No captures yet. Start streaming and capture images for analysis.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 192,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoCall, \"rKIYbGSYJleeF/eNe6jbXtQ9WeQ=\");\n_c = VideoCall;\nexport default VideoCall;\nvar _c;\n$RefreshReg$(_c, \"VideoCall\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "Webcam", "motion", "FaPlay", "FaStop", "FaCamera", "FaImage", "FaVideo", "FaCog", "jsxDEV", "_jsxDEV", "VideoCall", "onConnectionStatus", "onStartAnalysis", "onImageCaptured", "onDetectionResults", "_s", "isStreaming", "setIsStreaming", "isCapturing", "setIsCapturing", "capturedImages", "setCapturedImages", "autoCapture", "setAutoCapture", "captureInterval", "setCaptureInterval", "availableCameras", "setAvailableCameras", "selectedCamera", "setSelectedCamera", "showCameraSettings", "setShowCameraSettings", "mirrorCamera", "setMirrorCamera", "webcamRef", "intervalRef", "getAvailableCameras", "devices", "navigator", "mediaDevices", "enumerateDevices", "videoDevices", "filter", "device", "kind", "usbCamera", "find", "label", "toLowerCase", "includes", "deviceId", "length", "error", "console", "videoConstraints", "width", "height", "exact", "undefined", "sendImageForAnalysis", "imageSrc", "base64Data", "replace", "blob", "fetch", "then", "res", "formData", "FormData", "append", "response", "method", "body", "ok", "Error", "results", "json", "annotatedImagePath", "captureImage", "current", "getScreenshot", "newImage", "id", "Date", "now", "src", "timestamp", "toISOString", "analyzed", "prev", "slice", "startAutoCapture", "clearInterval", "setInterval", "startStream", "stopStream", "stopAutoCapture", "toggleAutoCapture", "handleCameraChange", "setTimeout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "value", "onChange", "e", "target", "map", "camera", "type", "checked", "htmlFor", "button", "whileHover", "scale", "whileTap", "disabled", "Number", "ref", "audio", "screenshotFormat", "mirrored", "image", "div", "alt", "toLocaleTimeString", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/VideoCall.js"], "sourcesContent": ["import React, { useState, useRef, useEffect, useCallback } from 'react';\nimport Webcam from 'react-webcam';\nimport { motion } from 'framer-motion';\nimport { FaPlay, FaStop, FaCamera, FaImage, FaVideo, FaCog } from 'react-icons/fa';\nimport './VideoCall.css';\n\nconst VideoCall = ({ onConnectionStatus, onStartAnalysis, onImageCaptured, onDetectionResults }) => {\n  const [isStreaming, setIsStreaming] = useState(false);\n  const [isCapturing, setIsCapturing] = useState(false);\n  const [capturedImages, setCapturedImages] = useState([]);\n  const [autoCapture, setAutoCapture] = useState(true);\n  const [captureInterval, setCaptureInterval] = useState(5000); // 5 seconds\n  const [availableCameras, setAvailableCameras] = useState([]);\n  const [selectedCamera, setSelectedCamera] = useState(null);\n  const [showCameraSettings, setShowCameraSettings] = useState(false);\n  const [mirrorCamera, setMirrorCamera] = useState(true); // Enable mirroring by default for USB cameras\n  const webcamRef = useRef(null);\n  const intervalRef = useRef(null);\n\n  // Get available cameras\n  const getAvailableCameras = useCallback(async () => {\n    try {\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      const videoDevices = devices.filter(device => device.kind === 'videoinput');\n      \n      setAvailableCameras(videoDevices);\n      \n      // Auto-select first USB camera or fallback to first available\n      const usbCamera = videoDevices.find(device => \n        device.label.toLowerCase().includes('usb') || \n        device.label.toLowerCase().includes('external') ||\n        device.label.toLowerCase().includes('camera')\n      );\n      \n      if (usbCamera) {\n        setSelectedCamera(usbCamera.deviceId);\n      } else if (videoDevices.length > 0) {\n        setSelectedCamera(videoDevices[0].deviceId);\n      }\n    } catch (error) {\n      console.error('Error getting cameras:', error);\n    }\n  }, []);\n\n  const videoConstraints = {\n    width: 640,\n    height: 480,\n    deviceId: selectedCamera ? { exact: selectedCamera } : undefined\n  };\n\n  const sendImageForAnalysis = useCallback(async (imageSrc) => {\n    try {\n      onStartAnalysis();\n\n      // Convert base64 to blob\n      const base64Data = imageSrc.replace(/^data:image\\/jpeg;base64,/, '');\n      const blob = await fetch(`data:image/jpeg;base64,${base64Data}`).then(res => res.blob());\n\n      // Create FormData\n      const formData = new FormData();\n      formData.append('image', blob, 'capture.jpg');\n\n      // Send to backend for YOLOv8 analysis\n      const response = await fetch('/api/analyze', {\n        method: 'POST',\n        body: formData\n      });\n\n      if (!response.ok) {\n        throw new Error('Analysis failed');\n      }\n\n      const results = await response.json();\n      \n      if (onDetectionResults && results && results.results) {\n        onDetectionResults(results.results, results.annotatedImagePath);\n      }\n\n    } catch (error) {\n      console.error('Error sending image for analysis:', error);\n      if (onDetectionResults) {\n        onDetectionResults([], null);\n      }\n    }\n  }, [onStartAnalysis, onDetectionResults]);\n\n  const captureImage = useCallback(() => {\n    if (webcamRef.current && isStreaming) {\n      setIsCapturing(true);\n      const imageSrc = webcamRef.current.getScreenshot();\n\n      if (imageSrc) {\n        const newImage = {\n          id: Date.now(),\n          src: imageSrc,\n          timestamp: new Date().toISOString(),\n          analyzed: false\n        };\n\n        setCapturedImages(prev => [newImage, ...prev.slice(0, 9)]); // Keep last 10 images\n\n        // Pass the captured image to parent component for YOLOv8 analysis\n        if (onImageCaptured) {\n          onImageCaptured(imageSrc);\n        }\n\n        // Send to YOLOv8 analysis\n        sendImageForAnalysis(imageSrc);\n      }\n\n      setIsCapturing(false);\n    }\n  }, [isStreaming, onImageCaptured, sendImageForAnalysis]);\n\n  const startAutoCapture = useCallback(() => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n    }\n\n    intervalRef.current = setInterval(() => {\n      if (isStreaming && webcamRef.current) {\n        captureImage();\n      }\n    }, captureInterval);\n  }, [isStreaming, captureInterval, captureImage]);\n\n  const startStream = useCallback(() => {\n    if (!selectedCamera) {\n      return;\n    }\n    \n    setIsStreaming(true);\n    onConnectionStatus(true);\n\n    // Start automatic capture if enabled\n    if (autoCapture) {\n      startAutoCapture();\n    }\n  }, [autoCapture, onConnectionStatus, startAutoCapture, selectedCamera]);\n\n  const stopStream = useCallback(() => {\n    setIsStreaming(false);\n    onConnectionStatus(false);\n    stopAutoCapture();\n  }, [onConnectionStatus]);\n\n  const stopAutoCapture = () => {\n    if (intervalRef.current) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  };\n\n  const toggleAutoCapture = () => {\n    setAutoCapture(!autoCapture);\n    if (!autoCapture) {\n      startAutoCapture();\n    } else {\n      stopAutoCapture();\n    }\n  };\n\n  const handleCameraChange = (deviceId) => {\n    setSelectedCamera(deviceId);\n    if (isStreaming) {\n      stopStream();\n      setTimeout(() => {\n        startStream();\n      }, 500);\n    }\n  };\n\n  useEffect(() => {\n    getAvailableCameras();\n  }, [getAvailableCameras]);\n\n  useEffect(() => {\n    return () => {\n      stopAutoCapture();\n    };\n  }, []);\n\n  useEffect(() => {\n    if (autoCapture && isStreaming) {\n      startAutoCapture();\n    } else {\n      stopAutoCapture();\n    }\n  }, [autoCapture, captureInterval, isStreaming, startAutoCapture]);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Camera Selection */}\n      <div className=\"bg-gray-50 p-4 rounded-lg\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <h3 className=\"text-sm font-medium text-gray-700\">Camera Settings</h3>\n          <button\n            onClick={() => setShowCameraSettings(!showCameraSettings)}\n            className=\"flex items-center text-sm text-[#0077B6] hover:text-[#20B2AA]\"\n          >\n            <FaCog className=\"mr-1 h-4 w-4\" />\n            {showCameraSettings ? 'Hide' : 'Show'} Settings\n          </button>\n        </div>\n        \n        {showCameraSettings && (\n          <div className=\"space-y-3\">\n            <label className=\"block text-sm font-medium text-gray-700\">\n              Select Camera:\n            </label>\n            <select\n              value={selectedCamera || ''}\n              onChange={(e) => handleCameraChange(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] text-sm\"\n            >\n              <option value=\"\">Select a camera...</option>\n              {availableCameras.map((camera) => (\n                <option key={camera.deviceId} value={camera.deviceId}>\n                  {camera.label || `Camera ${camera.deviceId.slice(0, 8)}...`}\n                </option>\n              ))}\n            </select>\n            \n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"mirrorCamera\"\n                checked={mirrorCamera}\n                onChange={(e) => setMirrorCamera(e.target.checked)}\n                className=\"mr-2 h-4 w-4 text-[#0077B6] focus:ring-[#20B2AA] border-gray-300 rounded\"\n              />\n              <label htmlFor=\"mirrorCamera\" className=\"text-sm font-medium text-gray-700\">\n                Mirror Camera (Fix reversed image)\n              </label>\n            </div>\n            \n            <p className=\"text-xs text-gray-500\">\n              {availableCameras.length} camera(s) detected\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Video Controls */}\n      <div className=\"flex flex-wrap gap-3 items-center justify-between\">\n        <div className=\"flex gap-3\">\n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className={`flex items-center px-4 py-2 rounded-full font-medium transition-all duration-300 ${\n              isStreaming\n                ? 'bg-gradient-to-r from-[#ef4444] to-[#dc2626] text-white shadow-md hover:shadow-lg'\n                : 'bg-gradient-to-r from-[#22c55e] to-[#16a34a] text-white shadow-md hover:shadow-lg'\n            }`}\n            onClick={isStreaming ? stopStream : startStream}\n            disabled={!selectedCamera}\n          >\n            {isStreaming ? <FaStop className=\"mr-2 h-4 w-4\" /> : <FaPlay className=\"mr-2 h-4 w-4\" />}\n            {isStreaming ? 'Stop Stream' : 'Start Stream'}\n          </motion.button>\n\n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"flex items-center px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed\"\n            onClick={captureImage}\n            disabled={!isStreaming || isCapturing}\n          >\n            <FaCamera className=\"mr-2 h-4 w-4\" />\n            {isCapturing ? 'Capturing...' : 'Capture Image'}\n          </motion.button>\n        </div>\n\n        <div className=\"flex items-center gap-4\">\n          <label className=\"flex items-center text-sm font-medium text-gray-700\">\n            <input\n              type=\"checkbox\"\n              checked={autoCapture}\n              onChange={toggleAutoCapture}\n              disabled={!isStreaming}\n              className=\"mr-2 h-4 w-4 text-[#0077B6] focus:ring-[#20B2AA] border-gray-300 rounded\"\n            />\n            Auto Capture\n          </label>\n\n          {autoCapture && (\n            <select\n              value={captureInterval}\n              onChange={(e) => setCaptureInterval(Number(e.target.value))}\n              disabled={!isStreaming}\n              className=\"px-3 py-1 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA] text-sm\"\n            >\n              <option value={3000}>Every 3s</option>\n              <option value={5000}>Every 5s</option>\n              <option value={10000}>Every 10s</option>\n            </select>\n          )}\n\n          <label className=\"flex items-center text-sm font-medium text-gray-700\">\n            <input\n              type=\"checkbox\"\n              checked={mirrorCamera}\n              onChange={(e) => setMirrorCamera(e.target.checked)}\n              className=\"mr-2 h-4 w-4 text-[#0077B6] focus:ring-[#20B2AA] border-gray-300 rounded\"\n            />\n            Mirror\n          </label>\n        </div>\n      </div>\n\n      {/* Video Container */}\n      <div className=\"relative bg-gray-900 rounded-xl overflow-hidden shadow-lg\">\n        {isStreaming ? (\n          <Webcam\n            ref={webcamRef}\n            audio={false}\n            screenshotFormat=\"image/jpeg\"\n            videoConstraints={videoConstraints}\n            mirrored={mirrorCamera}\n            className=\"w-full h-80 md:h-96 lg:h-[500px] object-cover\"\n          />\n        ) : (\n          <div className=\"w-full h-80 md:h-96 lg:h-[500px] flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900\">\n            <div className=\"text-center text-white\">\n              <FaVideo className=\"h-16 w-16 mx-auto mb-4 text-gray-400\" />\n              <p className=\"text-lg font-medium\">Click \"Start Stream\" to begin video consultation</p>\n              <p className=\"text-sm text-gray-400 mt-2\">Live dental examination and analysis</p>\n            </div>\n          </div>\n        )}\n\n        {isCapturing && (\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\n            <div className=\"bg-white px-6 py-3 rounded-full flex items-center text-[#0077B6] font-medium\">\n              <FaCamera className=\"mr-2 h-5 w-5 animate-pulse\" />\n              Capturing...\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Recent Captures */}\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\n        <div className=\"flex items-center mb-4\">\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\n            <FaImage className=\"h-4 w-4\" />\n          </div>\n          <h3 className=\"text-lg font-semibold text-[#0077B6]\">Recent Captures ({capturedImages.length})</h3>\n        </div>\n\n        {capturedImages.length > 0 ? (\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n            {capturedImages.map((image) => (\n              <motion.div\n                key={image.id}\n                whileHover={{ scale: 1.05 }}\n                className=\"relative bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200\"\n              >\n                <img\n                  src={image.src}\n                  alt=\"Captured\"\n                  className=\"w-full h-24 object-cover\"\n                />\n                <div className=\"p-2\">\n                  <p className=\"text-xs text-gray-600 mb-1\">\n                    {new Date(image.timestamp).toLocaleTimeString()}\n                  </p>\n                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\n                    image.analyzed\n                      ? 'bg-green-100 text-green-800'\n                      : 'bg-yellow-100 text-yellow-800'\n                  }`}>\n                    {image.analyzed ? '✓ Analyzed' : '⏳ Pending'}\n                  </span>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-8 text-gray-500\">\n            <FaImage className=\"h-12 w-12 mx-auto mb-3 opacity-50\" />\n            <p>No captures yet. Start streaming and capture images for analysis.</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default VideoCall; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,QAAQ,gBAAgB;AAClF,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAC;EAAEC,kBAAkB;EAAEC,eAAe;EAAEC,eAAe;EAAEC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EAClG,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9D,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACkC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACxD,MAAMsC,SAAS,GAAGrC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMsC,WAAW,GAAGtC,MAAM,CAAC,IAAI,CAAC;;EAEhC;EACA,MAAMuC,mBAAmB,GAAGrC,WAAW,CAAC,YAAY;IAClD,IAAI;MACF,MAAMsC,OAAO,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,gBAAgB,CAAC,CAAC;MAC/D,MAAMC,YAAY,GAAGJ,OAAO,CAACK,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,YAAY,CAAC;MAE3EjB,mBAAmB,CAACc,YAAY,CAAC;;MAEjC;MACA,MAAMI,SAAS,GAAGJ,YAAY,CAACK,IAAI,CAACH,MAAM,IACxCA,MAAM,CAACI,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IAC1CN,MAAM,CAACI,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,UAAU,CAAC,IAC/CN,MAAM,CAACI,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAC9C,CAAC;MAED,IAAIJ,SAAS,EAAE;QACbhB,iBAAiB,CAACgB,SAAS,CAACK,QAAQ,CAAC;MACvC,CAAC,MAAM,IAAIT,YAAY,CAACU,MAAM,GAAG,CAAC,EAAE;QAClCtB,iBAAiB,CAACY,YAAY,CAAC,CAAC,CAAC,CAACS,QAAQ,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,gBAAgB,GAAG;IACvBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,GAAG;IACXN,QAAQ,EAAEtB,cAAc,GAAG;MAAE6B,KAAK,EAAE7B;IAAe,CAAC,GAAG8B;EACzD,CAAC;EAED,MAAMC,oBAAoB,GAAG5D,WAAW,CAAC,MAAO6D,QAAQ,IAAK;IAC3D,IAAI;MACFhD,eAAe,CAAC,CAAC;;MAEjB;MACA,MAAMiD,UAAU,GAAGD,QAAQ,CAACE,OAAO,CAAC,2BAA2B,EAAE,EAAE,CAAC;MACpE,MAAMC,IAAI,GAAG,MAAMC,KAAK,CAAC,0BAA0BH,UAAU,EAAE,CAAC,CAACI,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACH,IAAI,CAAC,CAAC,CAAC;;MAExF;MACA,MAAMI,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEN,IAAI,EAAE,aAAa,CAAC;;MAE7C;MACA,MAAMO,QAAQ,GAAG,MAAMN,KAAK,CAAC,cAAc,EAAE;QAC3CO,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEL;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,iBAAiB,CAAC;MACpC;MAEA,MAAMC,OAAO,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAErC,IAAI9D,kBAAkB,IAAI6D,OAAO,IAAIA,OAAO,CAACA,OAAO,EAAE;QACpD7D,kBAAkB,CAAC6D,OAAO,CAACA,OAAO,EAAEA,OAAO,CAACE,kBAAkB,CAAC;MACjE;IAEF,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAItC,kBAAkB,EAAE;QACtBA,kBAAkB,CAAC,EAAE,EAAE,IAAI,CAAC;MAC9B;IACF;EACF,CAAC,EAAE,CAACF,eAAe,EAAEE,kBAAkB,CAAC,CAAC;EAEzC,MAAMgE,YAAY,GAAG/E,WAAW,CAAC,MAAM;IACrC,IAAImC,SAAS,CAAC6C,OAAO,IAAI/D,WAAW,EAAE;MACpCG,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMyC,QAAQ,GAAG1B,SAAS,CAAC6C,OAAO,CAACC,aAAa,CAAC,CAAC;MAElD,IAAIpB,QAAQ,EAAE;QACZ,MAAMqB,QAAQ,GAAG;UACfC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;UACdC,GAAG,EAAEzB,QAAQ;UACb0B,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;UACnCC,QAAQ,EAAE;QACZ,CAAC;QAEDnE,iBAAiB,CAACoE,IAAI,IAAI,CAACR,QAAQ,EAAE,GAAGQ,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE5D;QACA,IAAI7E,eAAe,EAAE;UACnBA,eAAe,CAAC+C,QAAQ,CAAC;QAC3B;;QAEA;QACAD,oBAAoB,CAACC,QAAQ,CAAC;MAChC;MAEAzC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,CAACH,WAAW,EAAEH,eAAe,EAAE8C,oBAAoB,CAAC,CAAC;EAExD,MAAMgC,gBAAgB,GAAG5F,WAAW,CAAC,MAAM;IACzC,IAAIoC,WAAW,CAAC4C,OAAO,EAAE;MACvBa,aAAa,CAACzD,WAAW,CAAC4C,OAAO,CAAC;IACpC;IAEA5C,WAAW,CAAC4C,OAAO,GAAGc,WAAW,CAAC,MAAM;MACtC,IAAI7E,WAAW,IAAIkB,SAAS,CAAC6C,OAAO,EAAE;QACpCD,YAAY,CAAC,CAAC;MAChB;IACF,CAAC,EAAEtD,eAAe,CAAC;EACrB,CAAC,EAAE,CAACR,WAAW,EAAEQ,eAAe,EAAEsD,YAAY,CAAC,CAAC;EAEhD,MAAMgB,WAAW,GAAG/F,WAAW,CAAC,MAAM;IACpC,IAAI,CAAC6B,cAAc,EAAE;MACnB;IACF;IAEAX,cAAc,CAAC,IAAI,CAAC;IACpBN,kBAAkB,CAAC,IAAI,CAAC;;IAExB;IACA,IAAIW,WAAW,EAAE;MACfqE,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACrE,WAAW,EAAEX,kBAAkB,EAAEgF,gBAAgB,EAAE/D,cAAc,CAAC,CAAC;EAEvE,MAAMmE,UAAU,GAAGhG,WAAW,CAAC,MAAM;IACnCkB,cAAc,CAAC,KAAK,CAAC;IACrBN,kBAAkB,CAAC,KAAK,CAAC;IACzBqF,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACrF,kBAAkB,CAAC,CAAC;EAExB,MAAMqF,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI7D,WAAW,CAAC4C,OAAO,EAAE;MACvBa,aAAa,CAACzD,WAAW,CAAC4C,OAAO,CAAC;MAClC5C,WAAW,CAAC4C,OAAO,GAAG,IAAI;IAC5B;EACF,CAAC;EAED,MAAMkB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B1E,cAAc,CAAC,CAACD,WAAW,CAAC;IAC5B,IAAI,CAACA,WAAW,EAAE;MAChBqE,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLK,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;EAED,MAAME,kBAAkB,GAAIhD,QAAQ,IAAK;IACvCrB,iBAAiB,CAACqB,QAAQ,CAAC;IAC3B,IAAIlC,WAAW,EAAE;MACf+E,UAAU,CAAC,CAAC;MACZI,UAAU,CAAC,MAAM;QACfL,WAAW,CAAC,CAAC;MACf,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC;EAEDhG,SAAS,CAAC,MAAM;IACdsC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;EAEzBtC,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXkG,eAAe,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENlG,SAAS,CAAC,MAAM;IACd,IAAIwB,WAAW,IAAIN,WAAW,EAAE;MAC9B2E,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLK,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAC1E,WAAW,EAAEE,eAAe,EAAER,WAAW,EAAE2E,gBAAgB,CAAC,CAAC;EAEjE,oBACElF,OAAA;IAAK2F,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB5F,OAAA;MAAK2F,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxC5F,OAAA;QAAK2F,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrD5F,OAAA;UAAI2F,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtEhG,OAAA;UACEiG,OAAO,EAAEA,CAAA,KAAM3E,qBAAqB,CAAC,CAACD,kBAAkB,CAAE;UAC1DsE,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAEzE5F,OAAA,CAACF,KAAK;YAAC6F,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACjC3E,kBAAkB,GAAG,MAAM,GAAG,MAAM,EAAC,WACxC;QAAA;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL3E,kBAAkB,iBACjBrB,OAAA;QAAK2F,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB5F,OAAA;UAAO2F,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRhG,OAAA;UACEkG,KAAK,EAAE/E,cAAc,IAAI,EAAG;UAC5BgF,QAAQ,EAAGC,CAAC,IAAKX,kBAAkB,CAACW,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACpDP,SAAS,EAAC,qHAAqH;UAAAC,QAAA,gBAE/H5F,OAAA;YAAQkG,KAAK,EAAC,EAAE;YAAAN,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAC3C/E,gBAAgB,CAACqF,GAAG,CAAEC,MAAM,iBAC3BvG,OAAA;YAA8BkG,KAAK,EAAEK,MAAM,CAAC9D,QAAS;YAAAmD,QAAA,EAClDW,MAAM,CAACjE,KAAK,IAAI,UAAUiE,MAAM,CAAC9D,QAAQ,CAACwC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;UAAK,GADhDsB,MAAM,CAAC9D,QAAQ;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEpB,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAEThG,OAAA;UAAK2F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC5F,OAAA;YACEwG,IAAI,EAAC,UAAU;YACf/B,EAAE,EAAC,cAAc;YACjBgC,OAAO,EAAElF,YAAa;YACtB4E,QAAQ,EAAGC,CAAC,IAAK5E,eAAe,CAAC4E,CAAC,CAACC,MAAM,CAACI,OAAO,CAAE;YACnDd,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,eACFhG,OAAA;YAAO0G,OAAO,EAAC,cAAc;YAACf,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAE5E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENhG,OAAA;UAAG2F,SAAS,EAAC,uBAAuB;UAAAC,QAAA,GACjC3E,gBAAgB,CAACyB,MAAM,EAAC,qBAC3B;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNhG,OAAA;MAAK2F,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBAChE5F,OAAA;QAAK2F,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzB5F,OAAA,CAACR,MAAM,CAACmH,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BlB,SAAS,EAAE,oFACTpF,WAAW,GACP,mFAAmF,GACnF,mFAAmF,EACtF;UACH0F,OAAO,EAAE1F,WAAW,GAAG+E,UAAU,GAAGD,WAAY;UAChD0B,QAAQ,EAAE,CAAC5F,cAAe;UAAAyE,QAAA,GAEzBrF,WAAW,gBAAGP,OAAA,CAACN,MAAM;YAACiG,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGhG,OAAA,CAACP,MAAM;YAACkG,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACvFzF,WAAW,GAAG,aAAa,GAAG,cAAc;QAAA;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAEhBhG,OAAA,CAACR,MAAM,CAACmH,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BlB,SAAS,EAAC,oNAAoN;UAC9NM,OAAO,EAAE5B,YAAa;UACtB0C,QAAQ,EAAE,CAACxG,WAAW,IAAIE,WAAY;UAAAmF,QAAA,gBAEtC5F,OAAA,CAACL,QAAQ;YAACgG,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACpCvF,WAAW,GAAG,cAAc,GAAG,eAAe;QAAA;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAENhG,OAAA;QAAK2F,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtC5F,OAAA;UAAO2F,SAAS,EAAC,qDAAqD;UAAAC,QAAA,gBACpE5F,OAAA;YACEwG,IAAI,EAAC,UAAU;YACfC,OAAO,EAAE5F,WAAY;YACrBsF,QAAQ,EAAEX,iBAAkB;YAC5BuB,QAAQ,EAAE,CAACxG,WAAY;YACvBoF,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,gBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAEPnF,WAAW,iBACVb,OAAA;UACEkG,KAAK,EAAEnF,eAAgB;UACvBoF,QAAQ,EAAGC,CAAC,IAAKpF,kBAAkB,CAACgG,MAAM,CAACZ,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAAE;UAC5Da,QAAQ,EAAE,CAACxG,WAAY;UACvBoF,SAAS,EAAC,8GAA8G;UAAAC,QAAA,gBAExH5F,OAAA;YAAQkG,KAAK,EAAE,IAAK;YAAAN,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtChG,OAAA;YAAQkG,KAAK,EAAE,IAAK;YAAAN,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtChG,OAAA;YAAQkG,KAAK,EAAE,KAAM;YAAAN,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACT,eAEDhG,OAAA;UAAO2F,SAAS,EAAC,qDAAqD;UAAAC,QAAA,gBACpE5F,OAAA;YACEwG,IAAI,EAAC,UAAU;YACfC,OAAO,EAAElF,YAAa;YACtB4E,QAAQ,EAAGC,CAAC,IAAK5E,eAAe,CAAC4E,CAAC,CAACC,MAAM,CAACI,OAAO,CAAE;YACnDd,SAAS,EAAC;UAA0E;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,UAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhG,OAAA;MAAK2F,SAAS,EAAC,2DAA2D;MAAAC,QAAA,GACvErF,WAAW,gBACVP,OAAA,CAACT,MAAM;QACL0H,GAAG,EAAExF,SAAU;QACfyF,KAAK,EAAE,KAAM;QACbC,gBAAgB,EAAC,YAAY;QAC7BtE,gBAAgB,EAAEA,gBAAiB;QACnCuE,QAAQ,EAAE7F,YAAa;QACvBoE,SAAS,EAAC;MAA+C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,gBAEFhG,OAAA;QAAK2F,SAAS,EAAC,+GAA+G;QAAAC,QAAA,eAC5H5F,OAAA;UAAK2F,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC5F,OAAA,CAACH,OAAO;YAAC8F,SAAS,EAAC;UAAsC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DhG,OAAA;YAAG2F,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAgD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvFhG,OAAA;YAAG2F,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAoC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAvF,WAAW,iBACVT,OAAA;QAAK2F,SAAS,EAAC,0EAA0E;QAAAC,QAAA,eACvF5F,OAAA;UAAK2F,SAAS,EAAC,8EAA8E;UAAAC,QAAA,gBAC3F5F,OAAA,CAACL,QAAQ;YAACgG,SAAS,EAAC;UAA4B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAErD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNhG,OAAA;MAAK2F,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvD5F,OAAA;QAAK2F,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC5F,OAAA;UAAK2F,SAAS,EAAC,6DAA6D;UAAAC,QAAA,eAC1E5F,OAAA,CAACJ,OAAO;YAAC+F,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNhG,OAAA;UAAI2F,SAAS,EAAC,sCAAsC;UAAAC,QAAA,GAAC,mBAAiB,EAACjF,cAAc,CAAC+B,MAAM,EAAC,GAAC;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChG,CAAC,EAELrF,cAAc,CAAC+B,MAAM,GAAG,CAAC,gBACxB1C,OAAA;QAAK2F,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEjF,cAAc,CAAC2F,GAAG,CAAEe,KAAK,iBACxBrH,OAAA,CAACR,MAAM,CAAC8H,GAAG;UAETV,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BlB,SAAS,EAAC,2HAA2H;UAAAC,QAAA,gBAErI5F,OAAA;YACE4E,GAAG,EAAEyC,KAAK,CAACzC,GAAI;YACf2C,GAAG,EAAC,UAAU;YACd5B,SAAS,EAAC;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACFhG,OAAA;YAAK2F,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB5F,OAAA;cAAG2F,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EACtC,IAAIlB,IAAI,CAAC2C,KAAK,CAACxC,SAAS,CAAC,CAAC2C,kBAAkB,CAAC;YAAC;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACJhG,OAAA;cAAM2F,SAAS,EAAE,uEACf0B,KAAK,CAACtC,QAAQ,GACV,6BAA6B,GAC7B,+BAA+B,EAClC;cAAAa,QAAA,EACAyB,KAAK,CAACtC,QAAQ,GAAG,YAAY,GAAG;YAAW;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GApBDqB,KAAK,CAAC5C,EAAE;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqBH,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENhG,OAAA;QAAK2F,SAAS,EAAC,gCAAgC;QAAAC,QAAA,gBAC7C5F,OAAA,CAACJ,OAAO;UAAC+F,SAAS,EAAC;QAAmC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDhG,OAAA;UAAA4F,QAAA,EAAG;QAAiE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1F,EAAA,CA7XIL,SAAS;AAAAwH,EAAA,GAATxH,SAAS;AA+Xf,eAAeA,SAAS;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}