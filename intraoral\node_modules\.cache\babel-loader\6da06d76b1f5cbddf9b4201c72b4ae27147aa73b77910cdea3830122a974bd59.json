{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\components\\\\YOLODetection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaBrain, FaChartBar, FaClock, FaImage, FaRobot, FaExclamationTriangle, FaCheckCircle, FaTimesCircle, FaCalendarAlt, FaTooth, FaEye, FaServer } from 'react-icons/fa';\nimport './YOLODetection.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst YOLODetection = ({\n  onResults,\n  isAnalyzing,\n  currentImage,\n  annotatedImagePath\n}) => {\n  _s();\n  const [detectionHistory, setDetectionHistory] = useState([]);\n  const [annotatedImage, setAnnotatedImage] = useState(null);\n  const [detectionStats, setDetectionStats] = useState({\n    decaycavity: 0,\n    'early-decay': 0,\n    'healthy tooth': 0\n  });\n  const [serverStatus, setServerStatus] = useState('checking');\n  const classColors = {\n    'decaycavity': '#ff6b6b',\n    'early-decay': '#ffd43b',\n    'healthy tooth': '#51cf66'\n  };\n  const classIcons = {\n    'decaycavity': /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n      className: \"h-6 w-6 text-red-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 20\n    }, this),\n    'early-decay': /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n      className: \"h-6 w-6 text-yellow-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 20\n    }, this),\n    'healthy tooth': /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n      className: \"h-6 w-6 text-green-500\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 22\n    }, this)\n  };\n\n  // Check server status\n  const checkServerStatus = useCallback(async () => {\n    try {\n      const response = await fetch('/api/health');\n      if (response.ok) {\n        const data = await response.json();\n        setServerStatus('connected');\n        console.log('✅ Server connected:', data);\n      } else {\n        setServerStatus('error');\n        console.log('❌ Server health check failed');\n      }\n    } catch (error) {\n      setServerStatus('disconnected');\n      console.log('❌ Server not reachable:', error.message);\n    }\n  }, []);\n  useEffect(() => {\n    checkServerStatus();\n    // Check server status every 30 seconds\n    const interval = setInterval(checkServerStatus, 30000);\n    return () => clearInterval(interval);\n  }, [checkServerStatus]);\n  useEffect(() => {\n    // Update current image when prop changes\n    if (currentImage) {\n      console.log('📸 Received new captured image for analysis');\n    }\n  }, [currentImage]);\n  useEffect(() => {\n    // No longer processing annotated images with bounding boxes\n    // This effect is kept for compatibility but does nothing\n  }, [annotatedImagePath]);\n  const drawDetectionBoxes = useCallback((ctx, results, canvasWidth, canvasHeight) => {\n    // Bounding boxes removed - no longer drawing detection boxes on images\n    // This function is kept for compatibility but does not draw anything\n  }, []);\n  const generateAnnotatedImage = useCallback(results => {\n    // No longer generating annotated images with bounding boxes\n    // This function is kept for compatibility but does nothing\n    setAnnotatedImage(null);\n  }, []);\n  const handleDetectionResults = useCallback(results => {\n    const newDetection = {\n      id: Date.now(),\n      timestamp: new Date().toISOString(),\n      results: results,\n      image: currentImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5Ij5JbWFnZSBQbGFjZWhvbGRlcjwvdGV4dD4KPC9zdmc+',\n      annotatedImage: annotatedImage,\n      // Extract filename from the full path for the API endpoint\n      annotatedImageUrl: annotatedImagePath ? `/api/annotated-image/${annotatedImagePath.split(/[\\/\\\\]/).pop()}` : null\n    };\n    setDetectionHistory(prev => [newDetection, ...prev.slice(0, 9)]);\n\n    // Update stats\n    const newStats = {\n      ...detectionStats\n    };\n    results.forEach(result => {\n      newStats[result.class]++;\n    });\n    setDetectionStats(newStats);\n\n    // Notify parent component\n    onResults(results);\n  }, [currentImage, detectionStats, onResults, annotatedImage, annotatedImagePath]);\n  const getConfidenceColor = confidence => {\n    if (confidence >= 0.8) return '#51cf66';\n    if (confidence >= 0.6) return '#ffd43b';\n    return '#ff6b6b';\n  };\n  const getServerStatusColor = () => {\n    switch (serverStatus) {\n      case 'connected':\n        return 'text-green-600';\n      case 'disconnected':\n        return 'text-red-600';\n      case 'error':\n        return 'text-yellow-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n  const getServerStatusText = () => {\n    switch (serverStatus) {\n      case 'connected':\n        return 'Connected';\n      case 'disconnected':\n        return 'Disconnected';\n      case 'error':\n        return 'Error';\n      default:\n        return 'Checking...';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaBrain, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"AI Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\",\n          children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n            className: \"inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), \"Model: best.pt\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"px-3 py-1 bg-[rgba(32,178,170,0.1)] text-[#20B2AA] rounded-full text-xs font-medium\",\n          children: \"Classes: 3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `px-3 py-1 bg-[rgba(0,119,182,0.1)] rounded-full text-xs font-medium flex items-center ${getServerStatusColor()}`,\n          children: [/*#__PURE__*/_jsxDEV(FaServer, {\n            className: \"inline mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), getServerStatusText()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), serverStatus === 'disconnected' && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-red-50 border border-red-200 p-4 rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n          className: \"h-5 w-5 text-red-500 mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-red-800\",\n            children: \"YOLOv8 Server Not Connected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-red-700 mt-1\",\n            children: [\"Please start the backend server to enable AI analysis. Run: \", /*#__PURE__*/_jsxDEV(\"code\", {\n              className: \"bg-red-100 px-2 py-1 rounded\",\n              children: \"npm run server\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 77\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 9\n    }, this), isAnalyzing && serverStatus === 'connected' && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[#20B2AA] border-opacity-30\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-[#0077B6] font-medium\",\n          children: \"Analyzing image with YOLOv8...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 9\n    }, this), !isAnalyzing && serverStatus === 'disconnected' && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-gray-50 p-6 rounded-lg border border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(FaServer, {\n          className: \"h-8 w-8 text-gray-400 mr-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"AI analysis unavailable - server not connected\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Detection Statistics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n        children: Object.entries(detectionStats).map(([className, count]) => /*#__PURE__*/_jsxDEV(motion.div, {\n          whileHover: {\n            scale: 1.05\n          },\n          className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mr-3\",\n              children: classIcons[className]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-700 capitalize\",\n                children: className.replace('-', ' ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-[#0077B6]\",\n                children: count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)\n        }, className, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n          children: /*#__PURE__*/_jsxDEV(FaClock, {\n            className: \"h-4 w-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-semibold text-[#0077B6]\",\n          children: \"Recent Detections\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3 max-h-64 overflow-y-auto\",\n        children: detectionHistory.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8\",\n          children: [/*#__PURE__*/_jsxDEV(FaEye, {\n            className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 font-medium mb-2\",\n            children: \"No detections yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 text-sm\",\n            children: \"Start video stream to begin analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this) : detectionHistory.map(detection => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          className: \"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 min-w-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaTooth, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 259,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: [detection.results.length, \" detection(s)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: new Date(detection.timestamp).toLocaleTimeString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 265,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 flex-wrap\",\n                children: [detection.results.slice(0, 3).map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center bg-gray-50 px-2 py-1 rounded\",\n                  children: [classIcons[result.class], /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1 text-xs font-medium text-gray-600\",\n                    children: [(result.confidence * 100).toFixed(0), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 27\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 25\n                }, this)), detection.results.length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [\"+\", detection.results.length - 3, \" more\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 17\n          }, this)\n        }, detection.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 5\n  }, this);\n};\n_s(YOLODetection, \"hXwQl/iZg0rTCZof/ZN5+R/ZqOw=\");\n_c = YOLODetection;\nexport default YOLODetection;\nvar _c;\n$RefreshReg$(_c, \"YOLODetection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "FaBrain", "FaChartBar", "FaClock", "FaImage", "FaRobot", "FaExclamationTriangle", "FaCheckCircle", "FaTimesCircle", "FaCalendarAlt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaEye", "FaServer", "jsxDEV", "_jsxDEV", "YOLODetection", "onResults", "isAnalyzing", "currentImage", "annotatedImagePath", "_s", "detectionHistory", "setDetectionHistory", "annotatedImage", "setAnnotatedImage", "detectionStats", "setDetectionStats", "decaycavity", "serverStatus", "setServerStatus", "classColors", "classIcons", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "checkServerStatus", "response", "fetch", "ok", "data", "json", "console", "log", "error", "message", "interval", "setInterval", "clearInterval", "drawDetectionBoxes", "ctx", "results", "canvasWidth", "canvasHeight", "generateAnnotatedImage", "handleDetectionResults", "newDetection", "id", "Date", "now", "timestamp", "toISOString", "image", "annotatedImageUrl", "split", "pop", "prev", "slice", "newStats", "for<PERSON>ach", "result", "class", "getConfidenceColor", "confidence", "getServerStatusColor", "getServerStatusText", "children", "div", "initial", "opacity", "y", "animate", "Object", "entries", "map", "count", "whileHover", "scale", "replace", "length", "detection", "x", "toLocaleTimeString", "index", "toFixed", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/components/YOLODetection.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaBrain, FaChartBar, FaClock, FaImage, FaRobot, FaExclamationTriangle, FaCheckCircle, FaTimesCircle, FaCalendarAlt, FaTooth, FaEye, FaServer } from 'react-icons/fa';\nimport './YOLODetection.css';\n\nconst YOLODetection = ({ onResults, isAnalyzing, currentImage, annotatedImagePath }) => {\n  const [detectionHistory, setDetectionHistory] = useState([]);\n  const [annotatedImage, setAnnotatedImage] = useState(null);\n  const [detectionStats, setDetectionStats] = useState({\n    decaycavity: 0,\n    'early-decay': 0,\n    'healthy tooth': 0\n  });\n  const [serverStatus, setServerStatus] = useState('checking');\n\n  const classColors = {\n    'decaycavity': '#ff6b6b',\n    'early-decay': '#ffd43b',\n    'healthy tooth': '#51cf66'\n  };\n\n  const classIcons = {\n    'decaycavity': <FaTimesCircle className=\"h-6 w-6 text-red-500\" />,\n    'early-decay': <FaExclamationTriangle className=\"h-6 w-6 text-yellow-500\" />,\n    'healthy tooth': <FaCheckCircle className=\"h-6 w-6 text-green-500\" />\n  };\n\n  // Check server status\n  const checkServerStatus = useCallback(async () => {\n    try {\n      const response = await fetch('/api/health');\n      if (response.ok) {\n        const data = await response.json();\n        setServerStatus('connected');\n        console.log('✅ Server connected:', data);\n      } else {\n        setServerStatus('error');\n        console.log('❌ Server health check failed');\n      }\n    } catch (error) {\n      setServerStatus('disconnected');\n      console.log('❌ Server not reachable:', error.message);\n    }\n  }, []);\n\n  useEffect(() => {\n    checkServerStatus();\n    // Check server status every 30 seconds\n    const interval = setInterval(checkServerStatus, 30000);\n    return () => clearInterval(interval);\n  }, [checkServerStatus]);\n\n  useEffect(() => {\n    // Update current image when prop changes\n    if (currentImage) {\n      console.log('📸 Received new captured image for analysis');\n    }\n  }, [currentImage]);\n\n  useEffect(() => {\n    // No longer processing annotated images with bounding boxes\n    // This effect is kept for compatibility but does nothing\n  }, [annotatedImagePath]);\n\n  const drawDetectionBoxes = useCallback((ctx, results, canvasWidth, canvasHeight) => {\n    // Bounding boxes removed - no longer drawing detection boxes on images\n    // This function is kept for compatibility but does not draw anything\n  }, []);\n\n  const generateAnnotatedImage = useCallback((results) => {\n    // No longer generating annotated images with bounding boxes\n    // This function is kept for compatibility but does nothing\n    setAnnotatedImage(null);\n  }, []);\n\n  const handleDetectionResults = useCallback((results) => {\n    const newDetection = {\n      id: Date.now(),\n      timestamp: new Date().toISOString(),\n      results: results,\n      image: currentImage || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSIjOTk5Ij5JbWFnZSBQbGFjZWhvbGRlcjwvdGV4dD4KPC9zdmc+',\n      annotatedImage: annotatedImage,\n      // Extract filename from the full path for the API endpoint\n      annotatedImageUrl: annotatedImagePath \n        ? `/api/annotated-image/${annotatedImagePath.split(/[\\/\\\\]/).pop()}`\n        : null\n    };\n\n    setDetectionHistory(prev => [newDetection, ...prev.slice(0, 9)]);\n\n    // Update stats\n    const newStats = { ...detectionStats };\n    results.forEach(result => {\n      newStats[result.class]++;\n    });\n    setDetectionStats(newStats);\n\n    // Notify parent component\n    onResults(results);\n  }, [currentImage, detectionStats, onResults, annotatedImage, annotatedImagePath]);\n\n  const getConfidenceColor = (confidence) => {\n    if (confidence >= 0.8) return '#51cf66';\n    if (confidence >= 0.6) return '#ffd43b';\n    return '#ff6b6b';\n  };\n\n  const getServerStatusColor = () => {\n    switch (serverStatus) {\n      case 'connected': return 'text-green-600';\n      case 'disconnected': return 'text-red-600';\n      case 'error': return 'text-yellow-600';\n      default: return 'text-gray-600';\n    }\n  };\n\n  const getServerStatusText = () => {\n    switch (serverStatus) {\n      case 'connected': return 'Connected';\n      case 'disconnected': return 'Disconnected';\n      case 'error': return 'Error';\n      default: return 'Checking...';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with Model Info */}\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\n        <div className=\"flex items-center\">\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\n            <FaBrain className=\"h-4 w-4\" />\n          </div>\n          <h3 className=\"text-lg font-semibold text-[#0077B6]\">AI Analysis</h3>\n        </div>\n        <div className=\"flex gap-2\">\n          <span className=\"px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full text-xs font-medium\">\n            <FaRobot className=\"inline mr-1\" />\n            Model: best.pt\n          </span>\n          <span className=\"px-3 py-1 bg-[rgba(32,178,170,0.1)] text-[#20B2AA] rounded-full text-xs font-medium\">\n            Classes: 3\n          </span>\n          <span className={`px-3 py-1 bg-[rgba(0,119,182,0.1)] rounded-full text-xs font-medium flex items-center ${getServerStatusColor()}`}>\n            <FaServer className=\"inline mr-1\" />\n            {getServerStatusText()}\n          </span>\n        </div>\n      </div>\n\n      {/* Server Status Warning */}\n      {serverStatus === 'disconnected' && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"bg-red-50 border border-red-200 p-4 rounded-lg\"\n        >\n          <div className=\"flex items-center\">\n            <FaExclamationTriangle className=\"h-5 w-5 text-red-500 mr-3\" />\n            <div>\n              <h4 className=\"text-sm font-medium text-red-800\">YOLOv8 Server Not Connected</h4>\n              <p className=\"text-sm text-red-700 mt-1\">\n                Please start the backend server to enable AI analysis. Run: <code className=\"bg-red-100 px-2 py-1 rounded\">npm run server</code>\n              </p>\n            </div>\n          </div>\n        </motion.div>\n      )}\n\n      {/* Analysis Status */}\n      {isAnalyzing && serverStatus === 'connected' && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[#20B2AA] border-opacity-30\"\n        >\n          <div className=\"flex items-center justify-center\">\n            <div className=\"loading-spinner mr-3\"></div>\n            <p className=\"text-[#0077B6] font-medium\">Analyzing image with YOLOv8...</p>\n          </div>\n        </motion.div>\n      )}\n\n      {/* No Analysis Available */}\n      {!isAnalyzing && serverStatus === 'disconnected' && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"bg-gray-50 p-6 rounded-lg border border-gray-200\"\n        >\n          <div className=\"flex items-center justify-center\">\n            <FaServer className=\"h-8 w-8 text-gray-400 mr-3\" />\n            <p className=\"text-gray-600 font-medium\">AI analysis unavailable - server not connected</p>\n          </div>\n        </motion.div>\n      )}\n\n      {/* Annotated Image section removed - no longer displaying bounding boxes */}\n\n      {/* Detection Statistics */}\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\n        <div className=\"flex items-center mb-4\">\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\n            <FaChartBar className=\"h-4 w-4\" />\n          </div>\n          <h4 className=\"text-lg font-semibold text-[#0077B6]\">Detection Statistics</h4>\n        </div>\n        <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n          {Object.entries(detectionStats).map(([className, count]) => (\n            <motion.div\n              key={className}\n              whileHover={{ scale: 1.05 }}\n              className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\"\n            >\n              <div className=\"flex items-center\">\n                <div className=\"mr-3\">{classIcons[className]}</div>\n                <div>\n                  <p className=\"text-sm font-medium text-gray-700 capitalize\">{className.replace('-', ' ')}</p>\n                  <p className=\"text-2xl font-bold text-[#0077B6]\">{count}</p>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n\n      {/* Detection History */}\n      <div className=\"bg-[rgba(0,119,182,0.05)] p-6 rounded-lg\">\n        <div className=\"flex items-center mb-4\">\n          <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\n            <FaClock className=\"h-4 w-4\" />\n          </div>\n          <h4 className=\"text-lg font-semibold text-[#0077B6]\">Recent Detections</h4>\n        </div>\n\n        <div className=\"space-y-3 max-h-64 overflow-y-auto\">\n          {detectionHistory.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <FaEye className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\n              <p className=\"text-gray-600 font-medium mb-2\">No detections yet</p>\n              <p className=\"text-gray-500 text-sm\">Start video stream to begin analysis</p>\n            </div>\n          ) : (\n            detectionHistory.map((detection) => (\n              <motion.div\n                key={detection.id}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:border-[#20B2AA] transition-all duration-300\"\n              >\n                <div className=\"flex items-start space-x-3\">\n                  {/* Annotated Image Thumbnail removed - no longer showing bounding box images */}\n\n                  {/* Detection Info */}\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <div className=\"flex items-center\">\n                        <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\n                          <FaTooth className=\"h-4 w-4\" />\n                        </div>\n                        <div>\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {detection.results.length} detection(s)\n                          </p>\n                          <p className=\"text-xs text-gray-500\">\n                            {new Date(detection.timestamp).toLocaleTimeString()}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                    \n                    {/* Detection Results */}\n                    <div className=\"flex items-center space-x-2 flex-wrap\">\n                      {detection.results.slice(0, 3).map((result, index) => (\n                        <div key={index} className=\"flex items-center bg-gray-50 px-2 py-1 rounded\">\n                          {classIcons[result.class]}\n                          <span className=\"ml-1 text-xs font-medium text-gray-600\">\n                            {(result.confidence * 100).toFixed(0)}%\n                          </span>\n                        </div>\n                      ))}\n                      {detection.results.length > 3 && (\n                        <span className=\"text-xs text-gray-500\">\n                          +{detection.results.length - 3} more\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            ))\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default YOLODetection; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,gBAAgB;AAC7K,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,SAAS;EAAEC,WAAW;EAAEC,YAAY;EAAEC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EACtF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC;IACnD8B,WAAW,EAAE,CAAC;IACd,aAAa,EAAE,CAAC;IAChB,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,UAAU,CAAC;EAE5D,MAAMiC,WAAW,GAAG;IAClB,aAAa,EAAE,SAAS;IACxB,aAAa,EAAE,SAAS;IACxB,eAAe,EAAE;EACnB,CAAC;EAED,MAAMC,UAAU,GAAG;IACjB,aAAa,eAAEjB,OAAA,CAACN,aAAa;MAACwB,SAAS,EAAC;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjE,aAAa,eAAEtB,OAAA,CAACR,qBAAqB;MAAC0B,SAAS,EAAC;IAAyB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5E,eAAe,eAAEtB,OAAA,CAACP,aAAa;MAACyB,SAAS,EAAC;IAAwB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACtE,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGtC,WAAW,CAAC,YAAY;IAChD,IAAI;MACF,MAAMuC,QAAQ,GAAG,MAAMC,KAAK,CAAC,aAAa,CAAC;MAC3C,IAAID,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClCb,eAAe,CAAC,WAAW,CAAC;QAC5Bc,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,IAAI,CAAC;MAC1C,CAAC,MAAM;QACLZ,eAAe,CAAC,OAAO,CAAC;QACxBc,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdhB,eAAe,CAAC,cAAc,CAAC;MAC/Bc,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEC,KAAK,CAACC,OAAO,CAAC;IACvD;EACF,CAAC,EAAE,EAAE,CAAC;EAENhD,SAAS,CAAC,MAAM;IACduC,iBAAiB,CAAC,CAAC;IACnB;IACA,MAAMU,QAAQ,GAAGC,WAAW,CAACX,iBAAiB,EAAE,KAAK,CAAC;IACtD,OAAO,MAAMY,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACV,iBAAiB,CAAC,CAAC;EAEvBvC,SAAS,CAAC,MAAM;IACd;IACA,IAAIoB,YAAY,EAAE;MAChByB,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC5D;EACF,CAAC,EAAE,CAAC1B,YAAY,CAAC,CAAC;EAElBpB,SAAS,CAAC,MAAM;IACd;IACA;EAAA,CACD,EAAE,CAACqB,kBAAkB,CAAC,CAAC;EAExB,MAAM+B,kBAAkB,GAAGnD,WAAW,CAAC,CAACoD,GAAG,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,KAAK;IAClF;IACA;EAAA,CACD,EAAE,EAAE,CAAC;EAEN,MAAMC,sBAAsB,GAAGxD,WAAW,CAAEqD,OAAO,IAAK;IACtD;IACA;IACA5B,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgC,sBAAsB,GAAGzD,WAAW,CAAEqD,OAAO,IAAK;IACtD,MAAMK,YAAY,GAAG;MACnBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;MACnCV,OAAO,EAAEA,OAAO;MAChBW,KAAK,EAAE7C,YAAY,IAAI,gaAAga;MACvbK,cAAc,EAAEA,cAAc;MAC9B;MACAyC,iBAAiB,EAAE7C,kBAAkB,GACjC,wBAAwBA,kBAAkB,CAAC8C,KAAK,CAAC,QAAQ,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,GAClE;IACN,CAAC;IAED5C,mBAAmB,CAAC6C,IAAI,IAAI,CAACV,YAAY,EAAE,GAAGU,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhE;IACA,MAAMC,QAAQ,GAAG;MAAE,GAAG5C;IAAe,CAAC;IACtC2B,OAAO,CAACkB,OAAO,CAACC,MAAM,IAAI;MACxBF,QAAQ,CAACE,MAAM,CAACC,KAAK,CAAC,EAAE;IAC1B,CAAC,CAAC;IACF9C,iBAAiB,CAAC2C,QAAQ,CAAC;;IAE3B;IACArD,SAAS,CAACoC,OAAO,CAAC;EACpB,CAAC,EAAE,CAAClC,YAAY,EAAEO,cAAc,EAAET,SAAS,EAAEO,cAAc,EAAEJ,kBAAkB,CAAC,CAAC;EAEjF,MAAMsD,kBAAkB,GAAIC,UAAU,IAAK;IACzC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS;IACvC,OAAO,SAAS;EAClB,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,QAAQ/C,YAAY;MAClB,KAAK,WAAW;QAAE,OAAO,gBAAgB;MACzC,KAAK,cAAc;QAAE,OAAO,cAAc;MAC1C,KAAK,OAAO;QAAE,OAAO,iBAAiB;MACtC;QAAS,OAAO,eAAe;IACjC;EACF,CAAC;EAED,MAAMgD,mBAAmB,GAAGA,CAAA,KAAM;IAChC,QAAQhD,YAAY;MAClB,KAAK,WAAW;QAAE,OAAO,WAAW;MACpC,KAAK,cAAc;QAAE,OAAO,cAAc;MAC1C,KAAK,OAAO;QAAE,OAAO,OAAO;MAC5B;QAAS,OAAO,aAAa;IAC/B;EACF,CAAC;EAED,oBACEd,OAAA;IAAKkB,SAAS,EAAC,WAAW;IAAA6C,QAAA,gBAExB/D,OAAA;MAAKkB,SAAS,EAAC,6EAA6E;MAAA6C,QAAA,gBAC1F/D,OAAA;QAAKkB,SAAS,EAAC,mBAAmB;QAAA6C,QAAA,gBAChC/D,OAAA;UAAKkB,SAAS,EAAC,6DAA6D;UAAA6C,QAAA,eAC1E/D,OAAA,CAACb,OAAO;YAAC+B,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNtB,OAAA;UAAIkB,SAAS,EAAC,sCAAsC;UAAA6C,QAAA,EAAC;QAAW;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACNtB,OAAA;QAAKkB,SAAS,EAAC,YAAY;QAAA6C,QAAA,gBACzB/D,OAAA;UAAMkB,SAAS,EAAC,oFAAoF;UAAA6C,QAAA,gBAClG/D,OAAA,CAACT,OAAO;YAAC2B,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPtB,OAAA;UAAMkB,SAAS,EAAC,qFAAqF;UAAA6C,QAAA,EAAC;QAEtG;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPtB,OAAA;UAAMkB,SAAS,EAAE,yFAAyF2C,oBAAoB,CAAC,CAAC,EAAG;UAAAE,QAAA,gBACjI/D,OAAA,CAACF,QAAQ;YAACoB,SAAS,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnCwC,mBAAmB,CAAC,CAAC;QAAA;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLR,YAAY,KAAK,cAAc,iBAC9Bd,OAAA,CAACd,MAAM,CAAC8E,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BjD,SAAS,EAAC,gDAAgD;MAAA6C,QAAA,eAE1D/D,OAAA;QAAKkB,SAAS,EAAC,mBAAmB;QAAA6C,QAAA,gBAChC/D,OAAA,CAACR,qBAAqB;UAAC0B,SAAS,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/DtB,OAAA;UAAA+D,QAAA,gBACE/D,OAAA;YAAIkB,SAAS,EAAC,kCAAkC;YAAA6C,QAAA,EAAC;UAA2B;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjFtB,OAAA;YAAGkB,SAAS,EAAC,2BAA2B;YAAA6C,QAAA,GAAC,8DACqB,eAAA/D,OAAA;cAAMkB,SAAS,EAAC,8BAA8B;cAAA6C,QAAA,EAAC;YAAc;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,EAGAnB,WAAW,IAAIW,YAAY,KAAK,WAAW,iBAC1Cd,OAAA,CAACd,MAAM,CAAC8E,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BjD,SAAS,EAAC,oFAAoF;MAAA6C,QAAA,eAE9F/D,OAAA;QAAKkB,SAAS,EAAC,kCAAkC;QAAA6C,QAAA,gBAC/C/D,OAAA;UAAKkB,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5CtB,OAAA;UAAGkB,SAAS,EAAC,4BAA4B;UAAA6C,QAAA,EAAC;QAA8B;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,EAGA,CAACnB,WAAW,IAAIW,YAAY,KAAK,cAAc,iBAC9Cd,OAAA,CAACd,MAAM,CAAC8E,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BjD,SAAS,EAAC,kDAAkD;MAAA6C,QAAA,eAE5D/D,OAAA;QAAKkB,SAAS,EAAC,kCAAkC;QAAA6C,QAAA,gBAC/C/D,OAAA,CAACF,QAAQ;UAACoB,SAAS,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDtB,OAAA;UAAGkB,SAAS,EAAC,2BAA2B;UAAA6C,QAAA,EAAC;QAA8C;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,eAKDtB,OAAA;MAAKkB,SAAS,EAAC,0CAA0C;MAAA6C,QAAA,gBACvD/D,OAAA;QAAKkB,SAAS,EAAC,wBAAwB;QAAA6C,QAAA,gBACrC/D,OAAA;UAAKkB,SAAS,EAAC,6DAA6D;UAAA6C,QAAA,eAC1E/D,OAAA,CAACZ,UAAU;YAAC8B,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACNtB,OAAA;UAAIkB,SAAS,EAAC,sCAAsC;UAAA6C,QAAA,EAAC;QAAoB;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eACNtB,OAAA;QAAKkB,SAAS,EAAC,uCAAuC;QAAA6C,QAAA,EACnDM,MAAM,CAACC,OAAO,CAAC3D,cAAc,CAAC,CAAC4D,GAAG,CAAC,CAAC,CAACrD,SAAS,EAAEsD,KAAK,CAAC,kBACrDxE,OAAA,CAACd,MAAM,CAAC8E,GAAG;UAETS,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BxD,SAAS,EAAC,6GAA6G;UAAA6C,QAAA,eAEvH/D,OAAA;YAAKkB,SAAS,EAAC,mBAAmB;YAAA6C,QAAA,gBAChC/D,OAAA;cAAKkB,SAAS,EAAC,MAAM;cAAA6C,QAAA,EAAE9C,UAAU,CAACC,SAAS;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDtB,OAAA;cAAA+D,QAAA,gBACE/D,OAAA;gBAAGkB,SAAS,EAAC,8CAA8C;gBAAA6C,QAAA,EAAE7C,SAAS,CAACyD,OAAO,CAAC,GAAG,EAAE,GAAG;cAAC;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7FtB,OAAA;gBAAGkB,SAAS,EAAC,mCAAmC;gBAAA6C,QAAA,EAAES;cAAK;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAVDJ,SAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWJ,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtB,OAAA;MAAKkB,SAAS,EAAC,0CAA0C;MAAA6C,QAAA,gBACvD/D,OAAA;QAAKkB,SAAS,EAAC,wBAAwB;QAAA6C,QAAA,gBACrC/D,OAAA;UAAKkB,SAAS,EAAC,6DAA6D;UAAA6C,QAAA,eAC1E/D,OAAA,CAACX,OAAO;YAAC6B,SAAS,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACNtB,OAAA;UAAIkB,SAAS,EAAC,sCAAsC;UAAA6C,QAAA,EAAC;QAAiB;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eAENtB,OAAA;QAAKkB,SAAS,EAAC,oCAAoC;QAAA6C,QAAA,EAChDxD,gBAAgB,CAACqE,MAAM,KAAK,CAAC,gBAC5B5E,OAAA;UAAKkB,SAAS,EAAC,kBAAkB;UAAA6C,QAAA,gBAC/B/D,OAAA,CAACH,KAAK;YAACqB,SAAS,EAAC;UAAsC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1DtB,OAAA;YAAGkB,SAAS,EAAC,gCAAgC;YAAA6C,QAAA,EAAC;UAAiB;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnEtB,OAAA;YAAGkB,SAAS,EAAC,uBAAuB;YAAA6C,QAAA,EAAC;UAAoC;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,GAENf,gBAAgB,CAACgE,GAAG,CAAEM,SAAS,iBAC7B7E,OAAA,CAACd,MAAM,CAAC8E,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCV,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEY,CAAC,EAAE;UAAE,CAAE;UAC9B5D,SAAS,EAAC,6GAA6G;UAAA6C,QAAA,eAEvH/D,OAAA;YAAKkB,SAAS,EAAC,4BAA4B;YAAA6C,QAAA,eAIzC/D,OAAA;cAAKkB,SAAS,EAAC,gBAAgB;cAAA6C,QAAA,gBAC7B/D,OAAA;gBAAKkB,SAAS,EAAC,wCAAwC;gBAAA6C,QAAA,eACrD/D,OAAA;kBAAKkB,SAAS,EAAC,mBAAmB;kBAAA6C,QAAA,gBAChC/D,OAAA;oBAAKkB,SAAS,EAAC,6DAA6D;oBAAA6C,QAAA,eAC1E/D,OAAA,CAACJ,OAAO;sBAACsB,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNtB,OAAA;oBAAA+D,QAAA,gBACE/D,OAAA;sBAAGkB,SAAS,EAAC,mCAAmC;sBAAA6C,QAAA,GAC7Cc,SAAS,CAACvC,OAAO,CAACsC,MAAM,EAAC,eAC5B;oBAAA;sBAAAzD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACJtB,OAAA;sBAAGkB,SAAS,EAAC,uBAAuB;sBAAA6C,QAAA,EACjC,IAAIlB,IAAI,CAACgC,SAAS,CAAC9B,SAAS,CAAC,CAACgC,kBAAkB,CAAC;oBAAC;sBAAA5D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNtB,OAAA;gBAAKkB,SAAS,EAAC,uCAAuC;gBAAA6C,QAAA,GACnDc,SAAS,CAACvC,OAAO,CAACgB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACiB,GAAG,CAAC,CAACd,MAAM,EAAEuB,KAAK,kBAC/ChF,OAAA;kBAAiBkB,SAAS,EAAC,gDAAgD;kBAAA6C,QAAA,GACxE9C,UAAU,CAACwC,MAAM,CAACC,KAAK,CAAC,eACzB1D,OAAA;oBAAMkB,SAAS,EAAC,wCAAwC;oBAAA6C,QAAA,GACrD,CAACN,MAAM,CAACG,UAAU,GAAG,GAAG,EAAEqB,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;kBAAA;oBAAA9D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GAJC0D,KAAK;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKV,CACN,CAAC,EACDuD,SAAS,CAACvC,OAAO,CAACsC,MAAM,GAAG,CAAC,iBAC3B5E,OAAA;kBAAMkB,SAAS,EAAC,uBAAuB;kBAAA6C,QAAA,GAAC,GACrC,EAACc,SAAS,CAACvC,OAAO,CAACsC,MAAM,GAAG,CAAC,EAAC,OACjC;gBAAA;kBAAAzD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA3CDuD,SAAS,CAACjC,EAAE;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4CP,CACb;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CAnSIL,aAAa;AAAAiF,EAAA,GAAbjF,aAAa;AAqSnB,eAAeA,aAAa;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}