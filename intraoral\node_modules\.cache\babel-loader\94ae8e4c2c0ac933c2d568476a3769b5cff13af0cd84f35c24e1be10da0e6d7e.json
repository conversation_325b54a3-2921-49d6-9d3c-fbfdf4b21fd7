{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dentlyzer_Final - Copy\\\\intraoral\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { <PERSON>a<PERSON>oot<PERSON>, FaVideo, FaUser, FaChartBar, FaBrain, FaClock, FaEye, FaSearch, FaHistory, FaInfoCircle, FaTimesCircle, FaExclamationTriangle, FaCheckCircle, FaCalendarAlt, FaServer } from 'react-icons/fa';\nimport VideoCall from './components/VideoCall';\nimport YOLODetection from './components/YOLODetection';\nimport PatientInfo from './components/PatientInfo';\nimport AnalysisResults from './components/AnalysisResults';\nimport Sidebar from './components/Sidebar';\nimport Navbar from './components/Navbar';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  var _detectionResults$;\n  const [isConnected, setIsConnected] = useState(false);\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [serverStatus, setServerStatus] = useState('checking');\n  const [patientInfo] = useState({\n    name: 'John Doe',\n    id: 'P001',\n    age: 35,\n    lastVisit: '2024-01-15'\n  });\n  const [detectionResults, setDetectionResults] = useState([]);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [currentCapturedImage, setCurrentCapturedImage] = useState(null);\n  // Annotated image path removed - no longer using bounding box images\n  const [analysisHistory, setAnalysisHistory] = useState([]);\n\n  // Check server status\n  const checkServerStatus = async () => {\n    try {\n      const response = await fetch('/api/health');\n      if (response.ok) {\n        const data = await response.json();\n        setServerStatus('connected');\n        console.log('✅ YOLOv8 server connected:', data);\n      } else {\n        setServerStatus('error');\n        console.log('❌ YOLOv8 server health check failed');\n      }\n    } catch (error) {\n      setServerStatus('disconnected');\n      console.log('❌ YOLOv8 server not reachable:', error.message);\n    }\n  };\n  useEffect(() => {\n    checkServerStatus();\n    // Check server status every 30 seconds\n    const interval = setInterval(checkServerStatus, 30000);\n    return () => clearInterval(interval);\n  }, []);\n  const handleConnectionStatus = status => {\n    setIsConnected(status);\n  };\n  const handleDetectionResults = (results, annotatedImagePath) => {\n    setDetectionResults(results);\n    setIsAnalyzing(false);\n    // No longer storing annotated image path since bounding boxes are removed\n\n    // Add to history\n    const newAnalysis = {\n      id: Date.now(),\n      timestamp: new Date().toISOString(),\n      results: results,\n      image: currentCapturedImage,\n      // No longer storing annotated image since bounding boxes are removed\n      patientId: patientInfo.id,\n      patientName: patientInfo.name\n    };\n    setAnalysisHistory(prev => [newAnalysis, ...prev.slice(0, 19)]); // Keep last 20 analyses\n\n    if (results.length > 0) {\n      const classNames = results.map(result => result.class);\n      console.info(`Detected: ${classNames.join(', ')}`);\n    }\n  };\n  const startAnalysis = () => {\n    if (serverStatus !== 'connected') {\n      console.error('YOLOv8 server not connected. Please start the backend server first.');\n      return;\n    }\n    setIsAnalyzing(true);\n    console.info('Starting dental analysis...');\n  };\n  const handleImageCaptured = imageSrc => {\n    setCurrentCapturedImage(imageSrc);\n  };\n  const getServerStatusColor = () => {\n    switch (serverStatus) {\n      case 'connected':\n        return 'text-green-600';\n      case 'disconnected':\n        return 'text-red-600';\n      case 'error':\n        return 'text-yellow-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n  const getServerStatusText = () => {\n    switch (serverStatus) {\n      case 'connected':\n        return 'YOLOv8 Connected';\n      case 'disconnected':\n        return 'YOLOv8 Disconnected';\n      case 'error':\n        return 'YOLOv8 Error';\n      default:\n        return 'Checking...';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      isOpen: sidebarOpen,\n      setIsOpen: setSidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {\n        toggleSidebar: () => setSidebarOpen(!sidebarOpen),\n        isConnected: isConnected\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\",\n                  children: \"Intraoral Patient Dashboard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#333333]\",\n                  children: \"Real-time dental analysis and consultation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mt-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getServerStatusColor()}`,\n                    children: [/*#__PURE__*/_jsxDEV(FaServer, {\n                      className: \"mr-1 h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 23\n                    }, this), getServerStatusText()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(motion.button, {\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  whileTap: {\n                    scale: 0.95\n                  },\n                  onClick: startAnalysis,\n                  disabled: isAnalyzing || serverStatus !== 'connected',\n                  className: \"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed\",\n                  children: [/*#__PURE__*/_jsxDEV(FaBrain, {\n                    className: \"h-5 w-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 21\n                  }, this), isAnalyzing ? 'Analyzing...' : 'Start Analysis']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 19\n                }, this), serverStatus !== 'connected' && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-red-600 text-center\",\n                  children: \"YOLOv8 server required for analysis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.1\n              },\n              className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaUser, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 170,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Patient\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 173,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: patientInfo.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaVideo, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Video Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: isConnected ? 'Live' : 'Offline'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaEye, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Detections\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 197,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: detectionResults.length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 198,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\",\n                    children: /*#__PURE__*/_jsxDEV(FaHistory, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"Total Analyses\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-2xl font-bold text-[#0077B6]\",\n                      children: analysisHistory.length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 210,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-3 rounded-full ${serverStatus === 'connected' ? 'bg-green-100 text-green-600' : serverStatus === 'error' ? 'bg-yellow-100 text-yellow-600' : 'bg-red-100 text-red-600'}`,\n                    children: /*#__PURE__*/_jsxDEV(FaServer, {\n                      className: \"h-6 w-6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-sm font-medium text-gray-500\",\n                      children: \"YOLOv8 Server\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: `text-2xl font-bold ${getServerStatusColor()}`,\n                      children: serverStatus === 'connected' ? 'Online' : serverStatus === 'error' ? 'Error' : 'Offline'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: -20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: 0.2\n                },\n                className: \"xl:col-span-2 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaVideo, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Live Video Consultation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(VideoCall, {\n                  onConnectionStatus: handleConnectionStatus,\n                  onStartAnalysis: startAnalysis,\n                  onImageCaptured: handleImageCaptured,\n                  onDetectionResults: handleDetectionResults\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  x: 20\n                },\n                animate: {\n                  opacity: 1,\n                  x: 0\n                },\n                transition: {\n                  delay: 0.2\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaUser, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Patient Information\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(PatientInfo, {\n                  patient: patientInfo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 xl:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.3\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaBrain, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 281,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"AI Dental Analysis\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(YOLODetection, {\n                  onResults: handleDetectionResults,\n                  isAnalyzing: isAnalyzing,\n                  currentImage: currentCapturedImage\n                  // No longer passing annotated image path since bounding boxes are removed\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: 0.3\n                },\n                className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n                      className: \"h-5 w-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: \"text-xl font-bold text-[#0077B6]\",\n                    children: \"Analysis Results\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(AnalysisResults, {\n                  results: detectionResults,\n                  isAnalyzing: isAnalyzing,\n                  history: analysisHistory\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: 0.4\n              },\n              className: \"mt-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                  children: /*#__PURE__*/_jsxDEV(FaHistory, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-bold text-[#0077B6]\",\n                  children: \"Patient Summary & History\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaChartBar, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 333,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-[#0077B6]\",\n                      children: \"Current Summary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 335,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 21\n                  }, this), detectionResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[rgba(0,119,182,0.05)] p-4 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-medium text-gray-700\",\n                          children: \"Total Detections\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 342,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-lg font-bold text-[#0077B6]\",\n                          children: detectionResults.length\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 343,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 341,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-medium text-gray-700\",\n                          children: \"Primary Issue\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 346,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-semibold text-gray-900 capitalize\",\n                          children: ((_detectionResults$ = detectionResults[0]) === null || _detectionResults$ === void 0 ? void 0 : _detectionResults$.class.replace('-', ' ')) || 'None'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 347,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 345,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-2\",\n                      children: detectionResults.map((result, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between p-2 bg-gray-50 rounded\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-medium text-gray-700 capitalize\",\n                          children: result.class.replace('-', ' ')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 356,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-bold text-[#0077B6]\",\n                          children: [(result.confidence * 100).toFixed(1), \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 359,\n                          columnNumber: 31\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 355,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [/*#__PURE__*/_jsxDEV(FaSearch, {\n                      className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 368,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 font-medium mb-2\",\n                      children: \"No current analysis\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 369,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 text-sm\",\n                      children: \"Start analysis to see summary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 370,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 379,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 378,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-[#0077B6]\",\n                      children: \"Recommendations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 381,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 21\n                  }, this), detectionResults.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-3\",\n                    children: (() => {\n                      const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\n                      const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\n                      const healthyCount = detectionResults.filter(r => r.class === 'healthy tooth').length;\n                      const recommendations = [];\n                      if (decayCount > 0) {\n                        recommendations.push({\n                          type: 'urgent',\n                          title: 'Immediate Treatment',\n                          description: 'Cavities require immediate dental treatment.',\n                          icon: /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n                            className: \"h-4 w-4 text-red-500\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 397,\n                            columnNumber: 37\n                          }, this)\n                        });\n                      }\n                      if (earlyDecayCount > 0) {\n                        recommendations.push({\n                          type: 'warning',\n                          title: 'Preventive Care',\n                          description: 'Schedule follow-up for preventive treatment.',\n                          icon: /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                            className: \"h-4 w-4 text-yellow-500\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 406,\n                            columnNumber: 37\n                          }, this)\n                        });\n                      }\n                      if (healthyCount > 0) {\n                        recommendations.push({\n                          type: 'positive',\n                          title: 'Good Oral Health',\n                          description: 'Continue regular oral hygiene routine.',\n                          icon: /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                            className: \"h-4 w-4 text-green-500\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 415,\n                            columnNumber: 37\n                          }, this)\n                        });\n                      }\n                      recommendations.push({\n                        type: 'info',\n                        title: 'Regular Checkup',\n                        description: 'Schedule next checkup within 6 months.',\n                        icon: /*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                          className: \"h-4 w-4 text-blue-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 423,\n                          columnNumber: 35\n                        }, this)\n                      });\n                      return recommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-start p-3 bg-gray-50 rounded-lg\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mr-3 mt-1\",\n                          children: rec.icon\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 428,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                            className: \"text-sm font-semibold text-gray-900\",\n                            children: rec.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 432,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xs text-gray-600\",\n                            children: rec.description\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 433,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 431,\n                          columnNumber: 31\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 427,\n                        columnNumber: 29\n                      }, this));\n                    })()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [/*#__PURE__*/_jsxDEV(FaInfoCircle, {\n                      className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 font-medium mb-2\",\n                      children: \"No recommendations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 text-sm\",\n                      children: \"Complete analysis for recommendations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 443,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(FaHistory, {\n                        className: \"h-4 w-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 452,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-lg font-semibold text-[#0077B6]\",\n                      children: \"Analysis History\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 21\n                  }, this), analysisHistory.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-3 max-h-64 overflow-y-auto\",\n                    children: [analysisHistory.slice(0, 5).map((historyItem, index) => {\n                      const decayCount = historyItem.results.filter(r => r.class === 'decaycavity').length;\n                      const earlyDecayCount = historyItem.results.filter(r => r.class === 'early-decay').length;\n                      const healthyCount = historyItem.results.filter(r => r.class === 'healthy tooth').length;\n                      let severity = 'low';\n                      let icon = /*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                        className: \"h-4 w-4 text-green-500\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 465,\n                        columnNumber: 38\n                      }, this);\n                      if (decayCount > 0) {\n                        severity = 'high';\n                        icon = /*#__PURE__*/_jsxDEV(FaTimesCircle, {\n                          className: \"h-4 w-4 text-red-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 469,\n                          columnNumber: 36\n                        }, this);\n                      } else if (earlyDecayCount > 0) {\n                        severity = 'medium';\n                        icon = /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n                          className: \"h-4 w-4 text-yellow-500\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 472,\n                          columnNumber: 36\n                        }, this);\n                      }\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mr-3\",\n                            children: icon\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 478,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-sm font-medium text-gray-900\",\n                              children: [historyItem.results.length, \" detection(s)\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 482,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-xs text-gray-500\",\n                              children: new Date(historyItem.timestamp).toLocaleDateString()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 485,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 481,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 477,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"px-2 py-1 text-xs font-medium bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full\",\n                          children: severity\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 490,\n                          columnNumber: 31\n                        }, this)]\n                      }, historyItem.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 476,\n                        columnNumber: 29\n                      }, this);\n                    }), analysisHistory.length > 5 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center pt-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: [\"+\", analysisHistory.length - 5, \" more analyses\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 499,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 498,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [/*#__PURE__*/_jsxDEV(FaHistory, {\n                      className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 507,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 font-medium mb-2\",\n                      children: \"No analysis history\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 508,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 text-sm\",\n                      children: \"Perform analyses to build history\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 509,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"L+scVehRLI3v2I3J4Rhc/BKBb9I=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaVideo", "FaUser", "FaChartBar", "FaBrain", "FaClock", "FaEye", "FaSearch", "FaHistory", "FaInfoCircle", "FaTimesCircle", "FaExclamationTriangle", "FaCheckCircle", "FaCalendarAlt", "FaServer", "VideoCall", "YOLODetection", "PatientInfo", "AnalysisResults", "Sidebar", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "_s", "_detectionResults$", "isConnected", "setIsConnected", "sidebarOpen", "setSidebarOpen", "serverStatus", "setServerStatus", "patientInfo", "name", "id", "age", "lastVisit", "detectionResults", "setDetectionResults", "isAnalyzing", "setIsAnalyzing", "currentCapturedImage", "setCurrentCapturedImage", "analysisHistory", "setAnalysisHistory", "checkServerStatus", "response", "fetch", "ok", "data", "json", "console", "log", "error", "message", "interval", "setInterval", "clearInterval", "handleConnectionStatus", "status", "handleDetectionResults", "results", "annotatedImagePath", "newAnalysis", "Date", "now", "timestamp", "toISOString", "image", "patientId", "patientName", "prev", "slice", "length", "classNames", "map", "result", "class", "info", "join", "startAnalysis", "handleImageCaptured", "imageSrc", "getServerStatusColor", "getServerStatusText", "className", "children", "isOpen", "setIsOpen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toggleSidebar", "div", "initial", "opacity", "animate", "transition", "duration", "button", "whileHover", "scale", "whileTap", "onClick", "disabled", "y", "delay", "x", "onConnectionStatus", "onStartAnalysis", "onImageCaptured", "onDetectionResults", "patient", "onResults", "currentImage", "history", "replace", "index", "confidence", "toFixed", "decayCount", "filter", "r", "earlyDecayCount", "healthyCount", "recommendations", "push", "type", "title", "description", "icon", "rec", "historyItem", "severity", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["D:/Den<PERSON><PERSON>_Final - Copy/intraoral/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ide<PERSON>, <PERSON>a<PERSON>ser, FaChartBar, FaBrain, FaClock, FaEye, FaSearch, FaHistory, FaInfoCircle, FaTimesCircle, FaExclamationTriangle, FaCheckCircle, FaCalendarAlt, FaServer } from 'react-icons/fa';\nimport VideoCall from './components/VideoCall';\nimport YOLODetection from './components/YOLODetection';\nimport PatientInfo from './components/PatientInfo';\nimport AnalysisResults from './components/AnalysisResults';\nimport Sidebar from './components/Sidebar';\nimport Navbar from './components/Navbar';\nimport './App.css';\n\nfunction App() {\n  const [isConnected, setIsConnected] = useState(false);\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [serverStatus, setServerStatus] = useState('checking');\n  const [patientInfo] = useState({\n    name: '<PERSON>',\n    id: 'P001',\n    age: 35,\n    lastVisit: '2024-01-15'\n  });\n  const [detectionResults, setDetectionResults] = useState([]);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [currentCapturedImage, setCurrentCapturedImage] = useState(null);\n  // Annotated image path removed - no longer using bounding box images\n  const [analysisHistory, setAnalysisHistory] = useState([]);\n\n  // Check server status\n  const checkServerStatus = async () => {\n    try {\n      const response = await fetch('/api/health');\n      if (response.ok) {\n        const data = await response.json();\n        setServerStatus('connected');\n        console.log('✅ YOLOv8 server connected:', data);\n      } else {\n        setServerStatus('error');\n        console.log('❌ YOLOv8 server health check failed');\n      }\n    } catch (error) {\n      setServerStatus('disconnected');\n      console.log('❌ YOLOv8 server not reachable:', error.message);\n    }\n  };\n\n  useEffect(() => {\n    checkServerStatus();\n    // Check server status every 30 seconds\n    const interval = setInterval(checkServerStatus, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const handleConnectionStatus = (status) => {\n    setIsConnected(status);\n  };\n\n  const handleDetectionResults = (results, annotatedImagePath) => {\n    setDetectionResults(results);\n    setIsAnalyzing(false);\n    // No longer storing annotated image path since bounding boxes are removed\n\n    // Add to history\n    const newAnalysis = {\n      id: Date.now(),\n      timestamp: new Date().toISOString(),\n      results: results,\n      image: currentCapturedImage,\n      // No longer storing annotated image since bounding boxes are removed\n      patientId: patientInfo.id,\n      patientName: patientInfo.name\n    };\n    \n    setAnalysisHistory(prev => [newAnalysis, ...prev.slice(0, 19)]); // Keep last 20 analyses\n    \n    if (results.length > 0) {\n      const classNames = results.map(result => result.class);\n      console.info(`Detected: ${classNames.join(', ')}`);\n    }\n  };\n\n  const startAnalysis = () => {\n    if (serverStatus !== 'connected') {\n      console.error('YOLOv8 server not connected. Please start the backend server first.');\n      return;\n    }\n    \n    setIsAnalyzing(true);\n    console.info('Starting dental analysis...');\n  };\n\n  const handleImageCaptured = (imageSrc) => {\n    setCurrentCapturedImage(imageSrc);\n  };\n\n  const getServerStatusColor = () => {\n    switch (serverStatus) {\n      case 'connected': return 'text-green-600';\n      case 'disconnected': return 'text-red-600';\n      case 'error': return 'text-yellow-600';\n      default: return 'text-gray-600';\n    }\n  };\n\n  const getServerStatusText = () => {\n    switch (serverStatus) {\n      case 'connected': return 'YOLOv8 Connected';\n      case 'disconnected': return 'YOLOv8 Disconnected';\n      case 'error': return 'YOLOv8 Error';\n      default: return 'Checking...';\n    }\n  };\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />\n\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} isConnected={isConnected} />\n\n        <main className=\"flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white\">\n          <div className=\"max-w-7xl mx-auto\">\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5 }}\n            >\n              {/* Header */}\n              <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4\">\n                <div>\n                  <h1 className=\"text-3xl md:text-4xl font-bold text-[#0077B6] mb-1\">\n                    Intraoral Patient Dashboard\n                  </h1>\n                  <p className=\"text-[#333333]\">Real-time dental analysis and consultation</p>\n                  <div className=\"flex items-center mt-2\">\n                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getServerStatusColor()}`}>\n                      <FaServer className=\"mr-1 h-3 w-3\" />\n                      {getServerStatusText()}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"flex flex-col gap-3\">\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={startAnalysis}\n                    disabled={isAnalyzing || serverStatus !== 'connected'}\n                    className=\"w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    <FaBrain className=\"h-5 w-5 mr-2\" />\n                    {isAnalyzing ? 'Analyzing...' : 'Start Analysis'}\n                  </motion.button>\n                  {serverStatus !== 'connected' && (\n                    <p className=\"text-xs text-red-600 text-center\">\n                      YOLOv8 server required for analysis\n                    </p>\n                  )}\n                </div>\n              </div>\n\n              {/* Stats Cards */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.1 }}\n                className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8\"\n              >\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\n                  <div className=\"flex items-center\">\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\n                      <FaUser className=\"h-6 w-6\" />\n                    </div>\n                    <div className=\"ml-4\">\n                      <h2 className=\"text-sm font-medium text-gray-500\">Patient</h2>\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{patientInfo.name}</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\n                  <div className=\"flex items-center\">\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\n                      <FaVideo className=\"h-6 w-6\" />\n                    </div>\n                    <div className=\"ml-4\">\n                      <h2 className=\"text-sm font-medium text-gray-500\">Video Status</h2>\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{isConnected ? 'Live' : 'Offline'}</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\n                  <div className=\"flex items-center\">\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\n                      <FaEye className=\"h-6 w-6\" />\n                    </div>\n                    <div className=\"ml-4\">\n                      <h2 className=\"text-sm font-medium text-gray-500\">Detections</h2>\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{detectionResults.length}</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\n                  <div className=\"flex items-center\">\n                    <div className=\"p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]\">\n                      <FaHistory className=\"h-6 w-6\" />\n                    </div>\n                    <div className=\"ml-4\">\n                      <h2 className=\"text-sm font-medium text-gray-500\">Total Analyses</h2>\n                      <p className=\"text-2xl font-bold text-[#0077B6]\">{analysisHistory.length}</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\n                  <div className=\"flex items-center\">\n                    <div className={`p-3 rounded-full ${serverStatus === 'connected' ? 'bg-green-100 text-green-600' : serverStatus === 'error' ? 'bg-yellow-100 text-yellow-600' : 'bg-red-100 text-red-600'}`}>\n                      <FaServer className=\"h-6 w-6\" />\n                    </div>\n                    <div className=\"ml-4\">\n                      <h2 className=\"text-sm font-medium text-gray-500\">YOLOv8 Server</h2>\n                      <p className={`text-2xl font-bold ${getServerStatusColor()}`}>\n                        {serverStatus === 'connected' ? 'Online' : serverStatus === 'error' ? 'Error' : 'Offline'}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n\n              {/* Main Content - Split Layout */}\n              <div className=\"grid grid-cols-1 xl:grid-cols-3 gap-6 mb-8\">\n                {/* Live Video Section - Larger */}\n                <motion.div\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: 0.2 }}\n                  className=\"xl:col-span-2 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\n                >\n                  <div className=\"flex items-center mb-6\">\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\n                      <FaVideo className=\"h-5 w-5\" />\n                    </div>\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">Live Video Consultation</h2>\n                  </div>\n                  <VideoCall\n                    onConnectionStatus={handleConnectionStatus}\n                    onStartAnalysis={startAnalysis}\n                    onImageCaptured={handleImageCaptured}\n                    onDetectionResults={handleDetectionResults}\n                  />\n                </motion.div>\n\n                {/* Patient Info Section */}\n                <motion.div\n                  initial={{ opacity: 0, x: 20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: 0.2 }}\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\n                >\n                  <div className=\"flex items-center mb-6\">\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\n                      <FaUser className=\"h-5 w-5\" />\n                    </div>\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">Patient Information</h2>\n                  </div>\n                  <PatientInfo patient={patientInfo} />\n                </motion.div>\n              </div>\n\n              {/* Analysis and Results Section */}\n              <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6\">\n                {/* AI Analysis Section */}\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.3 }}\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\n                >\n                  <div className=\"flex items-center mb-6\">\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\n                      <FaBrain className=\"h-5 w-5\" />\n                    </div>\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">AI Dental Analysis</h2>\n                  </div>\n                  <YOLODetection\n                    onResults={handleDetectionResults}\n                    isAnalyzing={isAnalyzing}\n                    currentImage={currentCapturedImage}\n                    // No longer passing annotated image path since bounding boxes are removed\n                  />\n                </motion.div>\n\n                {/* Results Section */}\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.3 }}\n                  className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\"\n                >\n                  <div className=\"flex items-center mb-6\">\n                    <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\n                      <FaChartBar className=\"h-5 w-5\" />\n                    </div>\n                    <h2 className=\"text-xl font-bold text-[#0077B6]\">Analysis Results</h2>\n                  </div>\n                  <AnalysisResults\n                    results={detectionResults}\n                    isAnalyzing={isAnalyzing}\n                    history={analysisHistory}\n                  />\n                </motion.div>\n              </div>\n\n              {/* Static Patient Summary Section - Always Visible */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.4 }}\n                className=\"mt-8\"\n              >\n                <div className=\"flex items-center mb-6\">\n                  <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\n                    <FaHistory className=\"h-5 w-5\" />\n                  </div>\n                  <h2 className=\"text-2xl font-bold text-[#0077B6]\">Patient Summary & History</h2>\n                </div>\n\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n                  {/* Summary Card */}\n                  <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\n                    <div className=\"flex items-center mb-4\">\n                      <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\n                        <FaChartBar className=\"h-4 w-4\" />\n                      </div>\n                      <h3 className=\"text-lg font-semibold text-[#0077B6]\">Current Summary</h3>\n                    </div>\n                    \n                    {detectionResults.length > 0 ? (\n                      <div className=\"space-y-4\">\n                        <div className=\"bg-[rgba(0,119,182,0.05)] p-4 rounded-lg\">\n                          <div className=\"flex items-center justify-between mb-2\">\n                            <span className=\"text-sm font-medium text-gray-700\">Total Detections</span>\n                            <span className=\"text-lg font-bold text-[#0077B6]\">{detectionResults.length}</span>\n                          </div>\n                          <div className=\"flex items-center justify-between\">\n                            <span className=\"text-sm font-medium text-gray-700\">Primary Issue</span>\n                            <span className=\"text-sm font-semibold text-gray-900 capitalize\">\n                              {detectionResults[0]?.class.replace('-', ' ') || 'None'}\n                            </span>\n                          </div>\n                        </div>\n                        \n                        <div className=\"space-y-2\">\n                          {detectionResults.map((result, index) => (\n                            <div key={index} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                              <span className=\"text-sm font-medium text-gray-700 capitalize\">\n                                {result.class.replace('-', ' ')}\n                              </span>\n                              <span className=\"text-sm font-bold text-[#0077B6]\">\n                                {(result.confidence * 100).toFixed(1)}%\n                              </span>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    ) : (\n                      <div className=\"text-center py-8\">\n                        <FaSearch className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\n                        <p className=\"text-gray-600 font-medium mb-2\">No current analysis</p>\n                        <p className=\"text-gray-500 text-sm\">Start analysis to see summary</p>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Recommendations Card */}\n                  <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\n                    <div className=\"flex items-center mb-4\">\n                      <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\n                        <FaInfoCircle className=\"h-4 w-4\" />\n                      </div>\n                      <h3 className=\"text-lg font-semibold text-[#0077B6]\">Recommendations</h3>\n                    </div>\n                    \n                    {detectionResults.length > 0 ? (\n                      <div className=\"space-y-3\">\n                        {(() => {\n                          const decayCount = detectionResults.filter(r => r.class === 'decaycavity').length;\n                          const earlyDecayCount = detectionResults.filter(r => r.class === 'early-decay').length;\n                          const healthyCount = detectionResults.filter(r => r.class === 'healthy tooth').length;\n                          const recommendations = [];\n\n                          if (decayCount > 0) {\n                            recommendations.push({\n                              type: 'urgent',\n                              title: 'Immediate Treatment',\n                              description: 'Cavities require immediate dental treatment.',\n                              icon: <FaTimesCircle className=\"h-4 w-4 text-red-500\" />\n                            });\n                          }\n\n                          if (earlyDecayCount > 0) {\n                            recommendations.push({\n                              type: 'warning',\n                              title: 'Preventive Care',\n                              description: 'Schedule follow-up for preventive treatment.',\n                              icon: <FaExclamationTriangle className=\"h-4 w-4 text-yellow-500\" />\n                            });\n                          }\n\n                          if (healthyCount > 0) {\n                            recommendations.push({\n                              type: 'positive',\n                              title: 'Good Oral Health',\n                              description: 'Continue regular oral hygiene routine.',\n                              icon: <FaCheckCircle className=\"h-4 w-4 text-green-500\" />\n                            });\n                          }\n\n                          recommendations.push({\n                            type: 'info',\n                            title: 'Regular Checkup',\n                            description: 'Schedule next checkup within 6 months.',\n                            icon: <FaCalendarAlt className=\"h-4 w-4 text-blue-500\" />\n                          });\n\n                          return recommendations.map((rec, index) => (\n                            <div key={index} className=\"flex items-start p-3 bg-gray-50 rounded-lg\">\n                              <div className=\"mr-3 mt-1\">\n                                {rec.icon}\n                              </div>\n                              <div>\n                                <h4 className=\"text-sm font-semibold text-gray-900\">{rec.title}</h4>\n                                <p className=\"text-xs text-gray-600\">{rec.description}</p>\n                              </div>\n                            </div>\n                          ));\n                        })()}\n                      </div>\n                    ) : (\n                      <div className=\"text-center py-8\">\n                        <FaInfoCircle className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\n                        <p className=\"text-gray-600 font-medium mb-2\">No recommendations</p>\n                        <p className=\"text-gray-500 text-sm\">Complete analysis for recommendations</p>\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Analysis History Card */}\n                  <div className=\"bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6\">\n                    <div className=\"flex items-center mb-4\">\n                      <div className=\"p-2 rounded-lg bg-[rgba(0,119,182,0.1)] text-[#0077B6] mr-3\">\n                        <FaHistory className=\"h-4 w-4\" />\n                      </div>\n                      <h3 className=\"text-lg font-semibold text-[#0077B6]\">Analysis History</h3>\n                    </div>\n                    \n                    {analysisHistory.length > 0 ? (\n                      <div className=\"space-y-3 max-h-64 overflow-y-auto\">\n                        {analysisHistory.slice(0, 5).map((historyItem, index) => {\n                          const decayCount = historyItem.results.filter(r => r.class === 'decaycavity').length;\n                          const earlyDecayCount = historyItem.results.filter(r => r.class === 'early-decay').length;\n                          const healthyCount = historyItem.results.filter(r => r.class === 'healthy tooth').length;\n                          \n                          let severity = 'low';\n                          let icon = <FaCheckCircle className=\"h-4 w-4 text-green-500\" />;\n                          \n                          if (decayCount > 0) {\n                            severity = 'high';\n                            icon = <FaTimesCircle className=\"h-4 w-4 text-red-500\" />;\n                          } else if (earlyDecayCount > 0) {\n                            severity = 'medium';\n                            icon = <FaExclamationTriangle className=\"h-4 w-4 text-yellow-500\" />;\n                          }\n\n                          return (\n                            <div key={historyItem.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                              <div className=\"flex items-center\">\n                                <div className=\"mr-3\">\n                                  {icon}\n                                </div>\n                                <div>\n                                  <p className=\"text-sm font-medium text-gray-900\">\n                                    {historyItem.results.length} detection(s)\n                                  </p>\n                                  <p className=\"text-xs text-gray-500\">\n                                    {new Date(historyItem.timestamp).toLocaleDateString()}\n                                  </p>\n                                </div>\n                              </div>\n                              <span className=\"px-2 py-1 text-xs font-medium bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-full\">\n                                {severity}\n                              </span>\n                            </div>\n                          );\n                        })}\n                        \n                        {analysisHistory.length > 5 && (\n                          <div className=\"text-center pt-2\">\n                            <p className=\"text-xs text-gray-500\">\n                              +{analysisHistory.length - 5} more analyses\n                            </p>\n                          </div>\n                        )}\n                      </div>\n                    ) : (\n                      <div className=\"text-center py-8\">\n                        <FaHistory className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\n                        <p className=\"text-gray-600 font-medium mb-2\">No analysis history</p>\n                        <p className=\"text-gray-500 text-sm\">Perform analyses to build history</p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </motion.div>\n            </motion.div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,gBAAgB;AAC/M,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,kBAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,UAAU,CAAC;EAC5D,MAAM,CAACmC,WAAW,CAAC,GAAGnC,QAAQ,CAAC;IAC7BoC,IAAI,EAAE,UAAU;IAChBC,EAAE,EAAE,MAAM;IACVC,GAAG,EAAE,EAAE;IACPC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACtE;EACA,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAMgD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,aAAa,CAAC;MAC3C,IAAID,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClCnB,eAAe,CAAC,WAAW,CAAC;QAC5BoB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEH,IAAI,CAAC;MACjD,CAAC,MAAM;QACLlB,eAAe,CAAC,OAAO,CAAC;QACxBoB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MACpD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdtB,eAAe,CAAC,cAAc,CAAC;MAC/BoB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEC,KAAK,CAACC,OAAO,CAAC;IAC9D;EACF,CAAC;EAEDxD,SAAS,CAAC,MAAM;IACd+C,iBAAiB,CAAC,CAAC;IACnB;IACA,MAAMU,QAAQ,GAAGC,WAAW,CAACX,iBAAiB,EAAE,KAAK,CAAC;IACtD,OAAO,MAAMY,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,sBAAsB,GAAIC,MAAM,IAAK;IACzChC,cAAc,CAACgC,MAAM,CAAC;EACxB,CAAC;EAED,MAAMC,sBAAsB,GAAGA,CAACC,OAAO,EAAEC,kBAAkB,KAAK;IAC9DxB,mBAAmB,CAACuB,OAAO,CAAC;IAC5BrB,cAAc,CAAC,KAAK,CAAC;IACrB;;IAEA;IACA,MAAMuB,WAAW,GAAG;MAClB7B,EAAE,EAAE8B,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;MACnCN,OAAO,EAAEA,OAAO;MAChBO,KAAK,EAAE3B,oBAAoB;MAC3B;MACA4B,SAAS,EAAErC,WAAW,CAACE,EAAE;MACzBoC,WAAW,EAAEtC,WAAW,CAACC;IAC3B,CAAC;IAEDW,kBAAkB,CAAC2B,IAAI,IAAI,CAACR,WAAW,EAAE,GAAGQ,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEjE,IAAIX,OAAO,CAACY,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMC,UAAU,GAAGb,OAAO,CAACc,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,KAAK,CAAC;MACtD1B,OAAO,CAAC2B,IAAI,CAAC,aAAaJ,UAAU,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACpD;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIlD,YAAY,KAAK,WAAW,EAAE;MAChCqB,OAAO,CAACE,KAAK,CAAC,qEAAqE,CAAC;MACpF;IACF;IAEAb,cAAc,CAAC,IAAI,CAAC;IACpBW,OAAO,CAAC2B,IAAI,CAAC,6BAA6B,CAAC;EAC7C,CAAC;EAED,MAAMG,mBAAmB,GAAIC,QAAQ,IAAK;IACxCxC,uBAAuB,CAACwC,QAAQ,CAAC;EACnC,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,QAAQrD,YAAY;MAClB,KAAK,WAAW;QAAE,OAAO,gBAAgB;MACzC,KAAK,cAAc;QAAE,OAAO,cAAc;MAC1C,KAAK,OAAO;QAAE,OAAO,iBAAiB;MACtC;QAAS,OAAO,eAAe;IACjC;EACF,CAAC;EAED,MAAMsD,mBAAmB,GAAGA,CAAA,KAAM;IAChC,QAAQtD,YAAY;MAClB,KAAK,WAAW;QAAE,OAAO,kBAAkB;MAC3C,KAAK,cAAc;QAAE,OAAO,qBAAqB;MACjD,KAAK,OAAO;QAAE,OAAO,cAAc;MACnC;QAAS,OAAO,aAAa;IAC/B;EACF,CAAC;EAED,oBACER,OAAA;IAAK+D,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBACvChE,OAAA,CAACH,OAAO;MAACoE,MAAM,EAAE3D,WAAY;MAAC4D,SAAS,EAAE3D;IAAe;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE3DtE,OAAA;MAAK+D,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDhE,OAAA,CAACF,MAAM;QAACyE,aAAa,EAAEA,CAAA,KAAMhE,cAAc,CAAC,CAACD,WAAW,CAAE;QAACF,WAAW,EAAEA;MAAY;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvFtE,OAAA;QAAM+D,SAAS,EAAC,0FAA0F;QAAAC,QAAA,eACxGhE,OAAA;UAAK+D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChChE,OAAA,CAACvB,MAAM,CAAC+F,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBC,OAAO,EAAE;cAAED,OAAO,EAAE;YAAE,CAAE;YACxBE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAb,QAAA,gBAG9BhE,OAAA;cAAK+D,SAAS,EAAC,kFAAkF;cAAAC,QAAA,gBAC/FhE,OAAA;gBAAAgE,QAAA,gBACEhE,OAAA;kBAAI+D,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtE,OAAA;kBAAG+D,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAA0C;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5EtE,OAAA;kBAAK+D,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,eACrChE,OAAA;oBAAM+D,SAAS,EAAE,uEAAuEF,oBAAoB,CAAC,CAAC,EAAG;oBAAAG,QAAA,gBAC/GhE,OAAA,CAACR,QAAQ;sBAACuE,SAAS,EAAC;oBAAc;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACpCR,mBAAmB,CAAC,CAAC;kBAAA;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtE,OAAA;gBAAK+D,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClChE,OAAA,CAACvB,MAAM,CAACqG,MAAM;kBACZC,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAC1BE,OAAO,EAAExB,aAAc;kBACvByB,QAAQ,EAAElE,WAAW,IAAIT,YAAY,KAAK,WAAY;kBACtDuD,SAAS,EAAC,oPAAoP;kBAAAC,QAAA,gBAE9PhE,OAAA,CAAClB,OAAO;oBAACiF,SAAS,EAAC;kBAAc;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACnCrD,WAAW,GAAG,cAAc,GAAG,gBAAgB;gBAAA;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,EACf9D,YAAY,KAAK,WAAW,iBAC3BR,OAAA;kBAAG+D,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAEhD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtE,OAAA,CAACvB,MAAM,CAAC+F,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAG,CAAE;cAC/BT,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAE,CAAE;cAC9BR,UAAU,EAAE;gBAAES,KAAK,EAAE;cAAI,CAAE;cAC3BtB,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBAErEhE,OAAA;gBAAK+D,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIhE,OAAA;kBAAK+D,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChChE,OAAA;oBAAK+D,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvEhE,OAAA,CAACpB,MAAM;sBAACmF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACNtE,OAAA;oBAAK+D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBhE,OAAA;sBAAI+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9DtE,OAAA;sBAAG+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEtD,WAAW,CAACC;oBAAI;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtE,OAAA;gBAAK+D,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIhE,OAAA;kBAAK+D,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChChE,OAAA;oBAAK+D,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvEhE,OAAA,CAACrB,OAAO;sBAACoF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNtE,OAAA;oBAAK+D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBhE,OAAA;sBAAI+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAY;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnEtE,OAAA;sBAAG+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAE5D,WAAW,GAAG,MAAM,GAAG;oBAAS;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtE,OAAA;gBAAK+D,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIhE,OAAA;kBAAK+D,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChChE,OAAA;oBAAK+D,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvEhE,OAAA,CAAChB,KAAK;sBAAC+E,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,eACNtE,OAAA;oBAAK+D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBhE,OAAA;sBAAI+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjEtE,OAAA;sBAAG+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEjD,gBAAgB,CAACoC;oBAAM;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtE,OAAA;gBAAK+D,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIhE,OAAA;kBAAK+D,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChChE,OAAA;oBAAK+D,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvEhE,OAAA,CAACd,SAAS;sBAAC6E,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACNtE,OAAA;oBAAK+D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBhE,OAAA;sBAAI+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrEtE,OAAA;sBAAG+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAE3C,eAAe,CAAC8B;oBAAM;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtE,OAAA;gBAAK+D,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,eAChIhE,OAAA;kBAAK+D,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChChE,OAAA;oBAAK+D,SAAS,EAAE,oBAAoBvD,YAAY,KAAK,WAAW,GAAG,6BAA6B,GAAGA,YAAY,KAAK,OAAO,GAAG,+BAA+B,GAAG,yBAAyB,EAAG;oBAAAwD,QAAA,eAC1LhE,OAAA,CAACR,QAAQ;sBAACuE,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACNtE,OAAA;oBAAK+D,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBhE,OAAA;sBAAI+D,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAa;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpEtE,OAAA;sBAAG+D,SAAS,EAAE,sBAAsBF,oBAAoB,CAAC,CAAC,EAAG;sBAAAG,QAAA,EAC1DxD,YAAY,KAAK,WAAW,GAAG,QAAQ,GAAGA,YAAY,KAAK,OAAO,GAAG,OAAO,GAAG;oBAAS;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAGbtE,OAAA;cAAK+D,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAEzDhE,OAAA,CAACvB,MAAM,CAAC+F,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAChCX,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE;gBAAE,CAAE;gBAC9BV,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,iIAAiI;gBAAAC,QAAA,gBAE3IhE,OAAA;kBAAK+D,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChE,OAAA;oBAAK+D,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1EhE,OAAA,CAACrB,OAAO;sBAACoF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNtE,OAAA;oBAAI+D,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC,eACNtE,OAAA,CAACP,SAAS;kBACR8F,kBAAkB,EAAEnD,sBAAuB;kBAC3CoD,eAAe,EAAE9B,aAAc;kBAC/B+B,eAAe,EAAE9B,mBAAoB;kBACrC+B,kBAAkB,EAAEpD;gBAAuB;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGbtE,OAAA,CAACvB,MAAM,CAAC+F,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE;gBAAG,CAAE;gBAC/BX,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEY,CAAC,EAAE;gBAAE,CAAE;gBAC9BV,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HhE,OAAA;kBAAK+D,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChE,OAAA;oBAAK+D,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1EhE,OAAA,CAACpB,MAAM;sBAACmF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B,CAAC,eACNtE,OAAA;oBAAI+D,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACNtE,OAAA,CAACL,WAAW;kBAACgG,OAAO,EAAEjF;gBAAY;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNtE,OAAA;cAAK+D,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAEpDhE,OAAA,CAACvB,MAAM,CAAC+F,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAG,CAAE;gBAC/BT,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAE,CAAE;gBAC9BR,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HhE,OAAA;kBAAK+D,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChE,OAAA;oBAAK+D,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1EhE,OAAA,CAAClB,OAAO;sBAACiF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACNtE,OAAA;oBAAI+D,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC,eACNtE,OAAA,CAACN,aAAa;kBACZkG,SAAS,EAAEtD,sBAAuB;kBAClCrB,WAAW,EAAEA,WAAY;kBACzB4E,YAAY,EAAE1E;kBACd;gBAAA;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAGbtE,OAAA,CAACvB,MAAM,CAAC+F,GAAG;gBACTC,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAG,CAAE;gBAC/BT,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAEU,CAAC,EAAE;gBAAE,CAAE;gBAC9BR,UAAU,EAAE;kBAAES,KAAK,EAAE;gBAAI,CAAE;gBAC3BtB,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAE7HhE,OAAA;kBAAK+D,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrChE,OAAA;oBAAK+D,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,eAC1EhE,OAAA,CAACnB,UAAU;sBAACkF,SAAS,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACNtE,OAAA;oBAAI+D,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACNtE,OAAA,CAACJ,eAAe;kBACd2C,OAAO,EAAExB,gBAAiB;kBAC1BE,WAAW,EAAEA,WAAY;kBACzB6E,OAAO,EAAEzE;gBAAgB;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNtE,OAAA,CAACvB,MAAM,CAAC+F,GAAG;cACTC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAG,CAAE;cAC/BT,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEU,CAAC,EAAE;cAAE,CAAE;cAC9BR,UAAU,EAAE;gBAAES,KAAK,EAAE;cAAI,CAAE;cAC3BtB,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAEhBhE,OAAA;gBAAK+D,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrChE,OAAA;kBAAK+D,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,eAC1EhE,OAAA,CAACd,SAAS;oBAAC6E,SAAS,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,eACNtE,OAAA;kBAAI+D,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC,eAENtE,OAAA;gBAAK+D,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBAEpDhE,OAAA;kBAAK+D,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBAChIhE,OAAA;oBAAK+D,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrChE,OAAA;sBAAK+D,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAC1EhE,OAAA,CAACnB,UAAU;wBAACkF,SAAS,EAAC;sBAAS;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC,eACNtE,OAAA;sBAAI+D,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,EAELvD,gBAAgB,CAACoC,MAAM,GAAG,CAAC,gBAC1BnD,OAAA;oBAAK+D,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBhE,OAAA;sBAAK+D,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,gBACvDhE,OAAA;wBAAK+D,SAAS,EAAC,wCAAwC;wBAAAC,QAAA,gBACrDhE,OAAA;0BAAM+D,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAC;wBAAgB;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC3EtE,OAAA;0BAAM+D,SAAS,EAAC,kCAAkC;0BAAAC,QAAA,EAAEjD,gBAAgB,CAACoC;wBAAM;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChF,CAAC,eACNtE,OAAA;wBAAK+D,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDhE,OAAA;0BAAM+D,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAC;wBAAa;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACxEtE,OAAA;0BAAM+D,SAAS,EAAC,gDAAgD;0BAAAC,QAAA,EAC7D,EAAA7D,kBAAA,GAAAY,gBAAgB,CAAC,CAAC,CAAC,cAAAZ,kBAAA,uBAAnBA,kBAAA,CAAqBoD,KAAK,CAACwC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,KAAI;wBAAM;0BAAA5B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENtE,OAAA;sBAAK+D,SAAS,EAAC,WAAW;sBAAAC,QAAA,EACvBjD,gBAAgB,CAACsC,GAAG,CAAC,CAACC,MAAM,EAAE0C,KAAK,kBAClChG,OAAA;wBAAiB+D,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,gBACnFhE,OAAA;0BAAM+D,SAAS,EAAC,8CAA8C;0BAAAC,QAAA,EAC3DV,MAAM,CAACC,KAAK,CAACwC,OAAO,CAAC,GAAG,EAAE,GAAG;wBAAC;0BAAA5B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CAAC,eACPtE,OAAA;0BAAM+D,SAAS,EAAC,kCAAkC;0BAAAC,QAAA,GAC/C,CAACV,MAAM,CAAC2C,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GACxC;wBAAA;0BAAA/B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA,GANC0B,KAAK;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAOV,CACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAENtE,OAAA;oBAAK+D,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BhE,OAAA,CAACf,QAAQ;sBAAC8E,SAAS,EAAC;oBAAsC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7DtE,OAAA;sBAAG+D,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACrEtE,OAAA;sBAAG+D,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA6B;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGNtE,OAAA;kBAAK+D,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBAChIhE,OAAA;oBAAK+D,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrChE,OAAA;sBAAK+D,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAC1EhE,OAAA,CAACb,YAAY;wBAAC4E,SAAS,EAAC;sBAAS;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC,eACNtE,OAAA;sBAAI+D,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAe;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,EAELvD,gBAAgB,CAACoC,MAAM,GAAG,CAAC,gBAC1BnD,OAAA;oBAAK+D,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACvB,CAAC,MAAM;sBACN,MAAMmC,UAAU,GAAGpF,gBAAgB,CAACqF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAK,aAAa,CAAC,CAACJ,MAAM;sBACjF,MAAMmD,eAAe,GAAGvF,gBAAgB,CAACqF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAK,aAAa,CAAC,CAACJ,MAAM;sBACtF,MAAMoD,YAAY,GAAGxF,gBAAgB,CAACqF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAK,eAAe,CAAC,CAACJ,MAAM;sBACrF,MAAMqD,eAAe,GAAG,EAAE;sBAE1B,IAAIL,UAAU,GAAG,CAAC,EAAE;wBAClBK,eAAe,CAACC,IAAI,CAAC;0BACnBC,IAAI,EAAE,QAAQ;0BACdC,KAAK,EAAE,qBAAqB;0BAC5BC,WAAW,EAAE,8CAA8C;0BAC3DC,IAAI,eAAE7G,OAAA,CAACZ,aAAa;4BAAC2E,SAAS,EAAC;0BAAsB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBACzD,CAAC,CAAC;sBACJ;sBAEA,IAAIgC,eAAe,GAAG,CAAC,EAAE;wBACvBE,eAAe,CAACC,IAAI,CAAC;0BACnBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,iBAAiB;0BACxBC,WAAW,EAAE,8CAA8C;0BAC3DC,IAAI,eAAE7G,OAAA,CAACX,qBAAqB;4BAAC0E,SAAS,EAAC;0BAAyB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBACpE,CAAC,CAAC;sBACJ;sBAEA,IAAIiC,YAAY,GAAG,CAAC,EAAE;wBACpBC,eAAe,CAACC,IAAI,CAAC;0BACnBC,IAAI,EAAE,UAAU;0BAChBC,KAAK,EAAE,kBAAkB;0BACzBC,WAAW,EAAE,wCAAwC;0BACrDC,IAAI,eAAE7G,OAAA,CAACV,aAAa;4BAACyE,SAAS,EAAC;0BAAwB;4BAAAI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAC3D,CAAC,CAAC;sBACJ;sBAEAkC,eAAe,CAACC,IAAI,CAAC;wBACnBC,IAAI,EAAE,MAAM;wBACZC,KAAK,EAAE,iBAAiB;wBACxBC,WAAW,EAAE,wCAAwC;wBACrDC,IAAI,eAAE7G,OAAA,CAACT,aAAa;0BAACwE,SAAS,EAAC;wBAAuB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAC1D,CAAC,CAAC;sBAEF,OAAOkC,eAAe,CAACnD,GAAG,CAAC,CAACyD,GAAG,EAAEd,KAAK,kBACpChG,OAAA;wBAAiB+D,SAAS,EAAC,4CAA4C;wBAAAC,QAAA,gBACrEhE,OAAA;0BAAK+D,SAAS,EAAC,WAAW;0BAAAC,QAAA,EACvB8C,GAAG,CAACD;wBAAI;0BAAA1C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CAAC,eACNtE,OAAA;0BAAAgE,QAAA,gBACEhE,OAAA;4BAAI+D,SAAS,EAAC,qCAAqC;4BAAAC,QAAA,EAAE8C,GAAG,CAACH;0BAAK;4BAAAxC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACpEtE,OAAA;4BAAG+D,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAE8C,GAAG,CAACF;0BAAW;4BAAAzC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD,CAAC;sBAAA,GAPE0B,KAAK;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAQV,CACN,CAAC;oBACJ,CAAC,EAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,gBAENtE,OAAA;oBAAK+D,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BhE,OAAA,CAACb,YAAY;sBAAC4E,SAAS,EAAC;oBAAsC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACjEtE,OAAA;sBAAG+D,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACpEtE,OAAA;sBAAG+D,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAqC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAGNtE,OAAA;kBAAK+D,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBAChIhE,OAAA;oBAAK+D,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrChE,OAAA;sBAAK+D,SAAS,EAAC,6DAA6D;sBAAAC,QAAA,eAC1EhE,OAAA,CAACd,SAAS;wBAAC6E,SAAS,EAAC;sBAAS;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC,eACNtE,OAAA;sBAAI+D,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAC;oBAAgB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,EAELjD,eAAe,CAAC8B,MAAM,GAAG,CAAC,gBACzBnD,OAAA;oBAAK+D,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAChD3C,eAAe,CAAC6B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACG,GAAG,CAAC,CAAC0D,WAAW,EAAEf,KAAK,KAAK;sBACvD,MAAMG,UAAU,GAAGY,WAAW,CAACxE,OAAO,CAAC6D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAK,aAAa,CAAC,CAACJ,MAAM;sBACpF,MAAMmD,eAAe,GAAGS,WAAW,CAACxE,OAAO,CAAC6D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAK,aAAa,CAAC,CAACJ,MAAM;sBACzF,MAAMoD,YAAY,GAAGQ,WAAW,CAACxE,OAAO,CAAC6D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAK,eAAe,CAAC,CAACJ,MAAM;sBAExF,IAAI6D,QAAQ,GAAG,KAAK;sBACpB,IAAIH,IAAI,gBAAG7G,OAAA,CAACV,aAAa;wBAACyE,SAAS,EAAC;sBAAwB;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC;sBAE/D,IAAI6B,UAAU,GAAG,CAAC,EAAE;wBAClBa,QAAQ,GAAG,MAAM;wBACjBH,IAAI,gBAAG7G,OAAA,CAACZ,aAAa;0BAAC2E,SAAS,EAAC;wBAAsB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAC3D,CAAC,MAAM,IAAIgC,eAAe,GAAG,CAAC,EAAE;wBAC9BU,QAAQ,GAAG,QAAQ;wBACnBH,IAAI,gBAAG7G,OAAA,CAACX,qBAAqB;0BAAC0E,SAAS,EAAC;wBAAyB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBACtE;sBAEA,oBACEtE,OAAA;wBAA0B+D,SAAS,EAAC,6DAA6D;wBAAAC,QAAA,gBAC/FhE,OAAA;0BAAK+D,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,gBAChChE,OAAA;4BAAK+D,SAAS,EAAC,MAAM;4BAAAC,QAAA,EAClB6C;0BAAI;4BAAA1C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC,eACNtE,OAAA;4BAAAgE,QAAA,gBACEhE,OAAA;8BAAG+D,SAAS,EAAC,mCAAmC;8BAAAC,QAAA,GAC7C+C,WAAW,CAACxE,OAAO,CAACY,MAAM,EAAC,eAC9B;4BAAA;8BAAAgB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC,eACJtE,OAAA;8BAAG+D,SAAS,EAAC,uBAAuB;8BAAAC,QAAA,EACjC,IAAItB,IAAI,CAACqE,WAAW,CAACnE,SAAS,CAAC,CAACqE,kBAAkB,CAAC;4BAAC;8BAAA9C,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACpD,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNtE,OAAA;0BAAM+D,SAAS,EAAC,oFAAoF;0BAAAC,QAAA,EACjGgD;wBAAQ;0BAAA7C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA,GAhBCyC,WAAW,CAACnG,EAAE;wBAAAuD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAiBnB,CAAC;oBAEV,CAAC,CAAC,EAEDjD,eAAe,CAAC8B,MAAM,GAAG,CAAC,iBACzBnD,OAAA;sBAAK+D,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eAC/BhE,OAAA;wBAAG+D,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,GAAC,GAClC,EAAC3C,eAAe,CAAC8B,MAAM,GAAG,CAAC,EAAC,gBAC/B;sBAAA;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,gBAENtE,OAAA;oBAAK+D,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BhE,OAAA,CAACd,SAAS;sBAAC6E,SAAS,EAAC;oBAAsC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9DtE,OAAA;sBAAG+D,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACrEtE,OAAA;sBAAG+D,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAiC;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACpE,EAAA,CA7fQD,GAAG;AAAAiH,EAAA,GAAHjH,GAAG;AA+fZ,eAAeA,GAAG;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}